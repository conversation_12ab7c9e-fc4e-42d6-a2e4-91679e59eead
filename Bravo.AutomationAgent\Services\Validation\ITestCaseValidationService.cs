using Bravo.AutomationAgent.Models.Requests;

namespace Bravo.AutomationAgent.Services.Validation;

public interface ITestCaseValidationService
{
    ValidationResult ValidateTestCase(TestCaseRequest request);
    ValidationResult ValidateTimeout(int? timeout);
    ValidationResult ValidateTestCaseContent(string testCase);
}

public class ValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = [];
    public List<string> Warnings { get; set; } = [];
    
    public static ValidationResult Success() => new() { IsValid = true };
    
    public static ValidationResult Failure(params string[] errors) => new() 
    { 
        IsValid = false, 
        Errors = errors.ToList() 
    };
    
    public void AddError(string error)
    {
        IsValid = false;
        Errors.Add(error);
    }
    
    public void AddWarning(string warning)
    {
        Warnings.Add(warning);
    }
}
