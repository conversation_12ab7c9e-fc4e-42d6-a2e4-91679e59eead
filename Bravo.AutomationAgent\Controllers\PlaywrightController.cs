using Bravo.AutomationAgent.Models.Requests;
using Bravo.AutomationAgent.Services.Automation;
using Microsoft.AspNetCore.Mvc;

namespace Bravo.AutomationAgent.Controllers;

[ApiController]
[Route("api/[controller]")]
public class PlaywrightController(
    ITestExecutionEngine testExecutionEngine,
    ILogger<PlaywrightController> logger) : ControllerBase
{
    /// <summary>
    /// Execute a test case using the automation framework
    /// </summary>
    /// <param name="request">Test execution request with title, content, environment, etc.</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Structured test execution result</returns>
    [HttpPost("execute")]
    public async Task<IActionResult> ExecuteTest([FromBody] TestExecutionRequest request,
        CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid)
        {
            logger.LogWarning("Invalid test execution request received");
            return BadRequest(ModelState);
        }

        if (string.IsNullOrWhiteSpace(request.Title))
        {
            logger.LogWarning("Test title is required but was empty");
            return BadRequest(new { error = "Test title is required." });
        }

        if (string.IsNullOrWhiteSpace(request.Content))
        {
            logger.LogWarning("Test content is required but was empty");
            return BadRequest(new { error = "Test content is required." });
        }

        try
        {
            logger.LogInformation("Executing test: {Title} in environment: {Environment}",
                request.Title, request.Environment);

            var result = await testExecutionEngine.ExecuteTestAsync(request, cancellationToken);

            logger.LogInformation("Test execution completed with status: {Status} for test ID: {TestId}",
                result.Status, result.TestId);

            return Ok(result);
        }
        catch (OperationCanceledException)
        {
            logger.LogWarning("Test execution was cancelled for test: {Title}", request.Title);
            return StatusCode(408, new { error = "Test execution was cancelled due to timeout." });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error executing test: {Title}", request.Title);

            return StatusCode(500, new
            {
                error = ex.Message,
                testName = request.Title,
                status = "Failed",
                environment = request.Environment,
                testId = request.GetOrGenerateTestId()
            });
        }
    }

    /// <summary>
    /// Legacy endpoint for backward compatibility
    /// </summary>
    /// <param name="request">Simple prompt request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Legacy format result</returns>
    [HttpPost("run")]
    public async Task<IActionResult> RunPlaywright([FromBody] PromptRequest request,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(request.Prompt))
        {
            logger.LogWarning("Prompt is required but was empty");
            return BadRequest("Prompt is required.");
        }

        try
        {
            logger.LogInformation("Running legacy Playwright execution: {Prompt}", request.Prompt);

            // Convert legacy request to new format
            var testRequest = new TestExecutionRequest
            {
                Title = "Legacy Test Execution",
                Content = request.Prompt,
                Environment = "test",
                TestId = null // Will be auto-generated
            };

            var result = await testExecutionEngine.ExecuteTestAsync(testRequest, cancellationToken);

            logger.LogInformation("Legacy execution completed");
            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in legacy Playwright execution: {Prompt}", request.Prompt);
            return StatusCode(500, new { error = ex.Message });
        }
    }

    /// <summary>
    /// Health check endpoint
    /// </summary>
    /// <returns>Service health status</returns>
    [HttpGet("health")]
    public IActionResult HealthCheck()
    {
        return Ok(new
        {
            status = "healthy",
            timestamp = DateTime.UtcNow,
            version = "2.0.0",
            features = new
            {
                testExecution = true,
                screenshots = true,
                tracing = true,
                artifactManagement = true,
                centralizedLogging = true
            }
        });
    }
}