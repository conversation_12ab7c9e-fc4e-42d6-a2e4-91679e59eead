using Bravo.AutomationAgent.Models.Requests;
using Bravo.AutomationAgent.Services.Automation;
using Bravo.AutomationAgent.Services.ErrorHandling;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;

namespace Bravo.AutomationAgent.Controllers;

[ApiController]
[Route("api/[controller]")]
public class PlaywrightController(
    IPlaywrightService playwrightService,
    ITestExecutionEngine testExecutionEngine,
    IErrorHandlingService errorHandlingService,
    ILogger<PlaywrightController> logger) : ControllerBase
{
    /// <summary>
    /// Execute a test case using the enhanced automation framework
    /// </summary>
    /// <param name="request">Test case request with structured format</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Structured test execution result</returns>
    [HttpPost("execute")]
    public async Task<IActionResult> ExecuteTest([FromBody] TestCaseRequest request,
        CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid)
        {
            logger.LogWarning("Invalid test case request received");
            return BadRequest(ModelState);
        }

        if (string.IsNullOrWhiteSpace(request.TestCase))
        {
            logger.LogWarning("Test case is required but was empty");
            return BadRequest(new { error = "Test case is required." });
        }

        try
        {
            logger.LogInformation("Executing test case: {TestCase}", request.TestCase);

            var result = await testExecutionEngine.ExecuteTestAsync(request, cancellationToken);

            logger.LogInformation("Test execution completed with status: {Status}", result.Status);
            return Ok(result);
        }
        catch (OperationCanceledException)
        {
            logger.LogWarning("Test execution was cancelled");
            return StatusCode(408, new { error = "Test execution was cancelled due to timeout." });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error executing test case: {TestCase}", request.TestCase);

            // Create error result using error handling service
            var errorResult = await errorHandlingService.HandleTestErrorAsync(
                new Models.Responses.MissionResult
                {
                    UserPrompt = request.TestCase,
                    Status = "Failed",
                    Duration = 0,
                    Steps = []
                },
                ex,
                cancellationToken);

            return StatusCode(500, errorResult);
        }
    }

    /// <summary>
    /// Legacy endpoint for backward compatibility
    /// </summary>
    /// <param name="request">Simple prompt request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Legacy format result</returns>
    [HttpPost("run")]
    public async Task<IActionResult> RunPlaywright([FromBody] PromptRequest request,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(request.Prompt))
        {
            logger.LogWarning("Prompt is required but was empty");
            return BadRequest("Prompt is required.");
        }

        try
        {
            logger.LogInformation("Running legacy Playwright execution: {Prompt}", request.Prompt);

            var result = await playwrightService.RunPlaywrightAsync(request.Prompt, cancellationToken);

            logger.LogInformation("Legacy execution completed");
            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in legacy Playwright execution: {Prompt}", request.Prompt);
            return StatusCode(500, new { error = ex.Message });
        }
    }

    /// <summary>
    /// Health check endpoint
    /// </summary>
    /// <returns>Service health status</returns>
    [HttpGet("health")]
    public IActionResult HealthCheck()
    {
        return Ok(new
        {
            status = "healthy",
            timestamp = DateTime.UtcNow,
            version = "1.0.0",
            features = new
            {
                testExecution = true,
                screenshots = true,
                tracing = true,
                validation = true,
                errorHandling = true
            }
        });
    }
}