using Bravo.AutomationAgent.Models.Requests;
using Bravo.AutomationAgent.Services.Automation;
using Microsoft.AspNetCore.Mvc;

namespace Bravo.AutomationAgent.Controllers;

[ApiController]
[Route("api/[controller]")]
public class PlaywrightController(IPlaywrightService playwrightService) : ControllerBase
{
    [HttpPost("run")]
    public async Task<IActionResult> RunPlaywright([FromBody] PromptRequest request,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(request.Prompt))
            return BadRequest("Prompt is required.");
        try
        {
            var result = await playwrightService.RunPlaywrightAsync(request.Prompt, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            // Log error (add logging as needed)
            return StatusCode(500, new { error = ex.Message });
        }
    }
}