using Microsoft.Extensions.AI;
using System.Collections.Generic;
using Bravo.AutomationAgent.Models.Requests;
using Bravo.AutomationAgent.Models.Responses;
using Bravo.AutomationAgent.Services.Artifacts;
using Bravo.AutomationAgent.Services.Logging;

namespace Bravo.AutomationAgent.Services.ResultHandlers;

public interface IPlaywrightResultHandler
{
    Task<TestExecutionResult> HandleAsync(IList<ChatMessage> messages, TestExecutionRequest request, ITestLogger testLogger, IArtifactManager artifactManager);
}