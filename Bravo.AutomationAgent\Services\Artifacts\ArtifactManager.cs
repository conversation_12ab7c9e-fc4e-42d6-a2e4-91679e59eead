using Bravo.AutomationAgent.Models.Responses;
using System.Text.Json;

namespace Bravo.AutomationAgent.Services.Artifacts;

public class ArtifactManager(ILogger<ArtifactManager> logger) : IArtifactManager
{
    private readonly string _basePath = Path.Combine(Directory.GetCurrentDirectory(), "site");

    public string CreateTestDirectory(string environment, string testId)
    {
        var testPath = GetArtifactPath(environment, testId);
        
        try
        {
            if (Directory.Exists(testPath))
            {
                Directory.Delete(testPath, true);
            }
            
            Directory.CreateDirectory(testPath);
            logger.LogInformation("Created test directory: {TestPath}", testPath);
            return testPath;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to create test directory: {TestPath}", testPath);
            throw;
        }
    }

    public async Task<string> SaveLogAsync(string environment, string testId, string logContent)
    {
        var logPath = Path.Combine(GetArtifactPath(environment, testId), "log.txt");
        
        try
        {
            await File.WriteAllTextAsync(logPath, logContent);
            logger.LogDebug("Saved log file: {LogPath}", logPath);
            return "log.txt";
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to save log file: {LogPath}", logPath);
            throw;
        }
    }

    public async Task<string> SaveResultAsync(string environment, string testId, TestExecutionResult result)
    {
        var resultPath = Path.Combine(GetArtifactPath(environment, testId), "result.json");
        
        try
        {
            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
            
            var json = JsonSerializer.Serialize(result, options);
            await File.WriteAllTextAsync(resultPath, json);
            
            logger.LogInformation("Saved test result: {ResultPath}", resultPath);
            return "result.json";
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to save test result: {ResultPath}", resultPath);
            throw;
        }
    }

    public async Task<string?> SaveScreenshotAsync(string environment, string testId, string stepName, byte[] imageData)
    {
        try
        {
            var sanitizedStepName = SanitizeFileName(stepName);
            var fileName = $"screenshot-{sanitizedStepName}.png";
            var screenshotPath = Path.Combine(GetArtifactPath(environment, testId), fileName);
            
            await File.WriteAllBytesAsync(screenshotPath, imageData);
            logger.LogDebug("Saved screenshot: {ScreenshotPath}", screenshotPath);
            
            return fileName;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to save screenshot for step: {StepName}", stepName);
            return null;
        }
    }

    public async Task<string?> SaveTraceAsync(string environment, string testId, byte[] traceData)
    {
        try
        {
            var fileName = "trace.zip";
            var tracePath = Path.Combine(GetArtifactPath(environment, testId), fileName);
            
            await File.WriteAllBytesAsync(tracePath, traceData);
            logger.LogDebug("Saved trace: {TracePath}", tracePath);
            
            return fileName;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to save trace for test: {TestId}", testId);
            return null;
        }
    }

    public async Task<string?> SaveVideoAsync(string environment, string testId, byte[] videoData)
    {
        try
        {
            var fileName = "video.mp4";
            var videoPath = Path.Combine(GetArtifactPath(environment, testId), fileName);
            
            await File.WriteAllBytesAsync(videoPath, videoData);
            logger.LogDebug("Saved video: {VideoPath}", videoPath);
            
            return fileName;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to save video for test: {TestId}", testId);
            return null;
        }
    }

    public string GetArtifactPath(string environment, string testId)
    {
        return Path.Combine(_basePath, environment, testId);
    }

    public Task CleanupOldArtifactsAsync(int retentionDays = 7)
    {
        try
        {
            if (!Directory.Exists(_basePath))
                return Task.CompletedTask;

            var cutoffDate = DateTime.UtcNow.AddDays(-retentionDays);
            var environmentDirs = Directory.GetDirectories(_basePath);

            foreach (var envDir in environmentDirs)
            {
                var testDirs = Directory.GetDirectories(envDir);
                
                foreach (var testDir in testDirs)
                {
                    var dirInfo = new DirectoryInfo(testDir);
                    if (dirInfo.CreationTimeUtc < cutoffDate)
                    {
                        try
                        {
                            Directory.Delete(testDir, true);
                            logger.LogInformation("Cleaned up old test directory: {TestDir}", testDir);
                        }
                        catch (Exception ex)
                        {
                            logger.LogWarning(ex, "Failed to cleanup directory: {TestDir}", testDir);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to cleanup old artifacts");
        }

        return Task.CompletedTask;
    }

    private string SanitizeFileName(string fileName)
    {
        var invalidChars = Path.GetInvalidFileNameChars();
        var sanitized = new string(fileName.Where(c => !invalidChars.Contains(c)).ToArray());
        
        sanitized = sanitized.Replace(" ", "-").ToLowerInvariant();
        if (sanitized.Length > 50)
        {
            sanitized = sanitized.Substring(0, 50);
        }
        
        return sanitized.Trim('-');
    }
}
