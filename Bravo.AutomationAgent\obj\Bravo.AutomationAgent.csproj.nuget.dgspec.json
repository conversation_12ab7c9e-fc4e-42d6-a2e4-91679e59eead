{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\workspace\\Bravo.AutomationAi\\Bravo.AutomationAgent\\Bravo.AutomationAgent.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\workspace\\Bravo.AutomationAi\\Bravo.AutomationAgent\\Bravo.AutomationAgent.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\workspace\\Bravo.AutomationAi\\Bravo.AutomationAgent\\Bravo.AutomationAgent.csproj", "projectName": "Bravo.AutomationAgent", "projectPath": "C:\\Users\\<USER>\\Desktop\\workspace\\Bravo.AutomationAi\\Bravo.AutomationAgent\\Bravo.AutomationAgent.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\workspace\\Bravo.AutomationAi\\Bravo.AutomationAgent\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Azure.AI.OpenAI": {"target": "Package", "version": "[2.2.0-beta.4, )"}, "Azure.Identity": {"target": "Package", "version": "[1.14.1, )"}, "GeminiDotnet.Extensions.AI": {"target": "Package", "version": "[0.12.0, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.1, )"}, "Microsoft.Extensions.AI": {"target": "Package", "version": "[9.6.0, )"}, "Microsoft.Extensions.AI.OpenAI": {"target": "Package", "version": "[9.6.0-preview.1.25310.2, )"}, "ModelContextProtocol": {"target": "Package", "version": "[0.3.0-preview.1, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[9.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[9.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102/PortableRuntimeIdentifierGraph.json"}}}}}