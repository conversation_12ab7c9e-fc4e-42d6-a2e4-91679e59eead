using Bravo.AutomationAgent.Infrastructure;
using Bravo.AutomationAgent.Models.Responses;
using ModelContextProtocol.Client;
using Microsoft.Extensions.AI;
using Bravo.AutomationAgent.Services.ResultHandlers;

namespace Bravo.AutomationAgent.Services.Automation;

public class PlaywrightService(IChatClientFactory chatClientFactory, IPlaywrightResultHandler playwright<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ILogger<PlaywrightService> logger)
    : IPlaywrightService
{
    public async Task<MissionResult> RunPlaywrightAsync(string prompt, CancellationToken cancellation)
    {
        logger.LogInformation("Starting Playwright run with prompt: {Prompt}", prompt);
        var client = chatClientFactory.CreateClient();
        var mcpClient = await GetMcpClient(cancellation);
        logger.LogInformation("MCP client created and tools fetched");
        var tools = await mcpClient.ListToolsAsync(cancellationToken: cancellation);

        List<ChatMessage> messages = [new(ChatRole.User, prompt)];
        List<ChatResponseUpdate> updates = [];

        try
        {
            await foreach (var update in client
                               .GetStreamingResponseAsync(messages,
                                   new ChatOptions { Tools = [.. tools] },
                                   cancellation))
            {
                updates.Add(update);
            }

            messages.AddMessages(updates);
            logger.LogInformation("Received {Count} updates from streaming response", updates.Count);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during Playwright streaming response");
            throw;
        }

        return playwrightResultHandler.Handle(messages);
    }

    private async Task<IMcpClient> GetMcpClient(CancellationToken cancellation)
    {
        try
        {
            var mcpClient = await McpClientFactory.CreateAsync(
                new StdioClientTransport(new StdioClientTransportOptions
                {
                    Command = "npx",
                    Arguments = ["@playwright/mcp@latest", "--isolated"],
                    Name = $"Playwright Mcp {Guid.NewGuid()}"
                }), cancellationToken: cancellation);
            logger.LogInformation("MCP client successfully created");
            return mcpClient;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to create MCP client");
            throw;
        }
    }
}