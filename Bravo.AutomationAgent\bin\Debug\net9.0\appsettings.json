{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Chat": {"Provider": "OpenAI", "Model": "gpt-4o-mini"}, "OpenAI": {"ApiKey": "********************************************************************************************************************************************************************", "Model": "gpt-4o-mini"}, "Azure": {"ApiKey": "your-azure-key", "Endpoint": "https://your-azure-endpoint"}, "Gemini": {"ApiKey": "AIzaSyDvxFESliCpQtyHWwLyQc0g-hGYP9wPPL8", "Model": "gemini-2.0-flash-thinking-exp-01-21"}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "Logs/log-.txt", "rollingInterval": "Day"}}]}}