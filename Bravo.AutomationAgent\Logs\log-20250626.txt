2025-06-26 13:22:58.266 +07:00 [WRN] Failed to determine the https port for redirect.
2025-06-26 13:22:58.461 +07:00 [INF] Running legacy Playwright execution: Open tinhte forums
Open random topic and get the first comment from this topic
2025-06-26 13:22:58.467 +07:00 [INF] Starting Playwright run with prompt: Open tinhte forums
Open random topic and get the first comment from this topic
2025-06-26 13:22:58.553 +07:00 [INF] Creating chat client for provider: OpenAI, model: gpt-4o-mini
2025-06-26 13:24:38.700 +07:00 [WRN] Failed to determine the https port for redirect.
2025-06-26 13:24:38.852 +07:00 [INF] Running legacy Playwright execution: Open tinhte forums
Open random topic and get the first comment from this topic
2025-06-26 13:24:38.874 +07:00 [INF] Starting Playwright run with prompt: Open tinhte forums
Open random topic and get the first comment from this topic
2025-06-26 13:24:38.990 +07:00 [INF] Creating chat client for provider: Azure, model: gpt-4o-mini
2025-06-26 13:24:39.259 +07:00 [ERR] Error in legacy Playwright execution: Open tinhte forums
Open random topic and get the first comment from this topic
System.UriFormatException: Invalid URI: The format of the URI could not be determined.
   at System.Uri.CreateThis(String uri, Boolean dontEscape, UriKind uriKind, UriCreationOptions& creationOptions)
   at System.Uri..ctor(String uriString)
   at Bravo.AutomationAgent.Infrastructure.ChatClientFactory.CreateClient(String provider, String model) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Infrastructure\ChatClientFactory.cs:line 32
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 15
   at Bravo.AutomationAgent.Controllers.PlaywrightController.RunPlaywright(PromptRequest request, CancellationToken cancellationToken) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Controllers\PlaywrightController.cs:line 93
2025-06-26 13:26:52.831 +07:00 [INF] Running legacy Playwright execution: Open tinhte forums via the url tinhte.vn
Open random topic and get the first comment from this topic
2025-06-26 13:26:52.842 +07:00 [INF] Starting Playwright run with prompt: Open tinhte forums via the url tinhte.vn
Open random topic and get the first comment from this topic
2025-06-26 13:26:52.851 +07:00 [INF] Creating chat client for provider: Azure, model: gpt-4o-mini
2025-06-26 13:26:52.983 +07:00 [ERR] Error in legacy Playwright execution: Open tinhte forums via the url tinhte.vn
Open random topic and get the first comment from this topic
System.UriFormatException: Invalid URI: The format of the URI could not be determined.
   at System.Uri.CreateThis(String uri, Boolean dontEscape, UriKind uriKind, UriCreationOptions& creationOptions)
   at System.Uri..ctor(String uriString)
   at Bravo.AutomationAgent.Infrastructure.ChatClientFactory.CreateClient(String provider, String model) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Infrastructure\ChatClientFactory.cs:line 32
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 15
   at Bravo.AutomationAgent.Controllers.PlaywrightController.RunPlaywright(PromptRequest request, CancellationToken cancellationToken) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Controllers\PlaywrightController.cs:line 93
2025-06-26 13:27:11.990 +07:00 [INF] Running legacy Playwright execution: Open tinhte forums via the url https://tinhte.vn
Open random topic and get the first comment from this topic
2025-06-26 13:27:11.996 +07:00 [INF] Starting Playwright run with prompt: Open tinhte forums via the url https://tinhte.vn
Open random topic and get the first comment from this topic
2025-06-26 13:27:12.002 +07:00 [INF] Creating chat client for provider: Azure, model: gpt-4o-mini
2025-06-26 13:27:12.213 +07:00 [ERR] Error in legacy Playwright execution: Open tinhte forums via the url https://tinhte.vn
Open random topic and get the first comment from this topic
System.UriFormatException: Invalid URI: The format of the URI could not be determined.
   at System.Uri.CreateThis(String uri, Boolean dontEscape, UriKind uriKind, UriCreationOptions& creationOptions)
   at System.Uri..ctor(String uriString)
   at Bravo.AutomationAgent.Infrastructure.ChatClientFactory.CreateClient(String provider, String model) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Infrastructure\ChatClientFactory.cs:line 32
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 15
   at Bravo.AutomationAgent.Controllers.PlaywrightController.RunPlaywright(PromptRequest request, CancellationToken cancellationToken) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Controllers\PlaywrightController.cs:line 93
2025-06-26 13:29:59.055 +07:00 [WRN] Failed to determine the https port for redirect.
2025-06-26 13:29:59.202 +07:00 [INF] Running legacy Playwright execution: Open tinhte forums
Open random topic and get the first comment from this topic
2025-06-26 13:29:59.208 +07:00 [INF] Starting Playwright run with prompt: Open tinhte forums
Open random topic and get the first comment from this topic
2025-06-26 13:29:59.293 +07:00 [INF] Creating chat client for provider: Azure, model: gpt-4o-mini
2025-06-26 13:30:01.377 +07:00 [INF] MCP client successfully created
2025-06-26 13:30:01.378 +07:00 [INF] MCP client created and tools fetched
2025-06-26 13:30:02.726 +07:00 [ERR] Error during Playwright streaming response
System.ClientModel.ClientResultException: HTTP 404 (: 404)

Resource not found
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.<>c__DisplayClass10_0.<<CompleteChatStreamingAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+MoveNext()
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
2025-06-26 13:30:11.721 +07:00 [ERR] Error in legacy Playwright execution: Open tinhte forums
Open random topic and get the first comment from this topic
System.ClientModel.ClientResultException: HTTP 404 (: 404)

Resource not found
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.<>c__DisplayClass10_0.<<CompleteChatStreamingAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+MoveNext()
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Controllers.PlaywrightController.RunPlaywright(PromptRequest request, CancellationToken cancellationToken) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Controllers\PlaywrightController.cs:line 93
2025-06-26 13:34:17.317 +07:00 [WRN] Failed to determine the https port for redirect.
2025-06-26 13:34:17.431 +07:00 [INF] Running legacy Playwright execution: Open tinhte forums
Open random topic and get the first comment from this topic
2025-06-26 13:34:17.434 +07:00 [INF] Starting Playwright run with prompt: Open tinhte forums
Open random topic and get the first comment from this topic
2025-06-26 13:34:17.484 +07:00 [INF] Creating chat client for provider: Azure, model: gpt-4o-mini
2025-06-26 13:34:19.523 +07:00 [INF] MCP client successfully created
2025-06-26 13:34:19.524 +07:00 [INF] MCP client created and tools fetched
2025-06-26 13:34:21.029 +07:00 [ERR] Error during Playwright streaming response
System.ClientModel.ClientResultException: HTTP 404 (: 404)

Resource not found
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.<>c__DisplayClass10_0.<<CompleteChatStreamingAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+MoveNext()
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
2025-06-26 13:34:26.859 +07:00 [ERR] Error in legacy Playwright execution: Open tinhte forums
Open random topic and get the first comment from this topic
System.ClientModel.ClientResultException: HTTP 404 (: 404)

Resource not found
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.<>c__DisplayClass10_0.<<CompleteChatStreamingAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+MoveNext()
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Controllers.PlaywrightController.RunPlaywright(PromptRequest request, CancellationToken cancellationToken) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Controllers\PlaywrightController.cs:line 93
2025-06-26 13:35:05.623 +07:00 [WRN] Failed to determine the https port for redirect.
2025-06-26 13:35:05.738 +07:00 [INF] Running legacy Playwright execution: Open tinhte forums
Open random topic and get the first comment from this topic
2025-06-26 13:35:05.745 +07:00 [INF] Starting Playwright run with prompt: Open tinhte forums
Open random topic and get the first comment from this topic
2025-06-26 13:35:05.878 +07:00 [INF] Creating chat client for provider: Azure, model: o4-mini
2025-06-26 13:35:07.859 +07:00 [INF] MCP client successfully created
2025-06-26 13:35:07.860 +07:00 [INF] MCP client created and tools fetched
2025-06-26 13:35:15.864 +07:00 [ERR] Error during Playwright streaming response
System.ClientModel.ClientResultException: HTTP 404 (: 404)

Resource not found
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.<>c__DisplayClass10_0.<<CompleteChatStreamingAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+MoveNext()
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
2025-06-26 13:35:17.746 +07:00 [ERR] Error in legacy Playwright execution: Open tinhte forums
Open random topic and get the first comment from this topic
System.ClientModel.ClientResultException: HTTP 404 (: 404)

Resource not found
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.<>c__DisplayClass10_0.<<CompleteChatStreamingAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+MoveNext()
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Controllers.PlaywrightController.RunPlaywright(PromptRequest request, CancellationToken cancellationToken) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Controllers\PlaywrightController.cs:line 93
2025-06-26 13:42:20.667 +07:00 [WRN] Failed to determine the https port for redirect.
2025-06-26 13:42:21.197 +07:00 [INF] Running legacy Playwright execution: Open tinhte forums
Open random topic and get the first comment from this topic
2025-06-26 13:42:21.208 +07:00 [INF] Starting Playwright run with prompt: Open tinhte forums
Open random topic and get the first comment from this topic
2025-06-26 13:42:21.227 +07:00 [INF] Creating chat client for provider: Azure, model: o4-mini
2025-06-26 13:42:23.610 +07:00 [INF] MCP client successfully created
2025-06-26 13:42:23.620 +07:00 [INF] MCP client created and tools fetched
2025-06-26 13:42:25.212 +07:00 [ERR] Error during Playwright streaming response
System.ClientModel.ClientResultException: HTTP 404 (: 404)

Resource not found
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.<>c__DisplayClass10_0.<<CompleteChatStreamingAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+MoveNext()
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
2025-06-26 13:42:25.275 +07:00 [ERR] Error in legacy Playwright execution: Open tinhte forums
Open random topic and get the first comment from this topic
System.ClientModel.ClientResultException: HTTP 404 (: 404)

Resource not found
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.<>c__DisplayClass10_0.<<CompleteChatStreamingAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+MoveNext()
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Controllers.PlaywrightController.RunPlaywright(PromptRequest request, CancellationToken cancellationToken) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Controllers\PlaywrightController.cs:line 93
2025-06-26 13:45:59.639 +07:00 [WRN] Failed to determine the https port for redirect.
2025-06-26 13:45:59.769 +07:00 [INF] Running legacy Playwright execution: Open tinhte forums
Open random topic and get the first comment from this topic
2025-06-26 13:45:59.772 +07:00 [INF] Starting Playwright run with prompt: Open tinhte forums
Open random topic and get the first comment from this topic
2025-06-26 13:45:59.776 +07:00 [INF] Creating chat client for provider: Azure, model: o4-mini
2025-06-26 13:46:02.051 +07:00 [INF] MCP client successfully created
2025-06-26 13:46:02.054 +07:00 [INF] MCP client created and tools fetched
2025-06-26 13:46:03.444 +07:00 [ERR] Error during Playwright streaming response
System.ClientModel.ClientResultException: HTTP 404 (: 404)

Resource not found
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.<>c__DisplayClass10_0.<<CompleteChatStreamingAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+MoveNext()
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
2025-06-26 13:46:03.464 +07:00 [ERR] Error in legacy Playwright execution: Open tinhte forums
Open random topic and get the first comment from this topic
System.ClientModel.ClientResultException: HTTP 404 (: 404)

Resource not found
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.<>c__DisplayClass10_0.<<CompleteChatStreamingAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+MoveNext()
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Controllers.PlaywrightController.RunPlaywright(PromptRequest request, CancellationToken cancellationToken) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Controllers\PlaywrightController.cs:line 93
2025-06-26 13:48:30.858 +07:00 [WRN] Failed to determine the https port for redirect.
2025-06-26 13:48:30.978 +07:00 [INF] Running legacy Playwright execution: Open tinhte forums
Open random topic and get the first comment from this topic
2025-06-26 13:48:30.983 +07:00 [INF] Starting Playwright run with prompt: Open tinhte forums
Open random topic and get the first comment from this topic
2025-06-26 13:48:30.989 +07:00 [INF] Creating chat client for provider: Azure, model: o4-mini
2025-06-26 13:48:33.085 +07:00 [INF] MCP client successfully created
2025-06-26 13:48:33.088 +07:00 [INF] MCP client created and tools fetched
2025-06-26 13:48:34.143 +07:00 [ERR] Error during Playwright streaming response
System.ClientModel.ClientResultException: HTTP 404 (: 404)

Resource not found
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.<>c__DisplayClass10_0.<<CompleteChatStreamingAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+MoveNext()
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
2025-06-26 13:48:34.164 +07:00 [ERR] Error in legacy Playwright execution: Open tinhte forums
Open random topic and get the first comment from this topic
System.ClientModel.ClientResultException: HTTP 404 (: 404)

Resource not found
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.<>c__DisplayClass10_0.<<CompleteChatStreamingAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+MoveNext()
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Controllers.PlaywrightController.RunPlaywright(PromptRequest request, CancellationToken cancellationToken) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Controllers\PlaywrightController.cs:line 93
2025-06-26 13:51:37.041 +07:00 [WRN] Failed to determine the https port for redirect.
2025-06-26 13:51:37.754 +07:00 [INF] Running legacy Playwright execution: Open tinhte forums
Open random topic and get the first comment from this topic
2025-06-26 13:51:37.777 +07:00 [INF] Starting Playwright run with prompt: Open tinhte forums
Open random topic and get the first comment from this topic
2025-06-26 13:51:37.801 +07:00 [INF] Creating chat client for provider: Azure, model: gpt-4.1
2025-06-26 13:51:40.488 +07:00 [INF] MCP client successfully created
2025-06-26 13:51:40.493 +07:00 [INF] MCP client created and tools fetched
2025-06-26 13:51:42.027 +07:00 [ERR] Error during Playwright streaming response
System.ClientModel.ClientResultException: HTTP 404 (: 404)

Resource not found
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.<>c__DisplayClass10_0.<<CompleteChatStreamingAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+MoveNext()
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
2025-06-26 13:51:42.076 +07:00 [ERR] Error in legacy Playwright execution: Open tinhte forums
Open random topic and get the first comment from this topic
System.ClientModel.ClientResultException: HTTP 404 (: 404)

Resource not found
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.<>c__DisplayClass10_0.<<CompleteChatStreamingAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+MoveNext()
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Controllers.PlaywrightController.RunPlaywright(PromptRequest request, CancellationToken cancellationToken) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Controllers\PlaywrightController.cs:line 93
2025-06-26 13:53:06.282 +07:00 [WRN] Failed to determine the https port for redirect.
2025-06-26 13:53:06.396 +07:00 [INF] Running legacy Playwright execution: Open tinhte forums
Open random topic and get the first comment from this topic
2025-06-26 13:53:06.400 +07:00 [INF] Starting Playwright run with prompt: Open tinhte forums
Open random topic and get the first comment from this topic
2025-06-26 13:53:06.405 +07:00 [INF] Creating chat client for provider: Azure, model: gpt-4.1
2025-06-26 13:53:08.362 +07:00 [INF] MCP client successfully created
2025-06-26 13:53:08.364 +07:00 [INF] MCP client created and tools fetched
2025-06-26 13:53:09.478 +07:00 [ERR] Error during Playwright streaming response
System.ClientModel.ClientResultException: HTTP 404 (: 404)

Resource not found
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.<>c__DisplayClass10_0.<<CompleteChatStreamingAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+MoveNext()
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
2025-06-26 13:53:09.493 +07:00 [ERR] Error in legacy Playwright execution: Open tinhte forums
Open random topic and get the first comment from this topic
System.ClientModel.ClientResultException: HTTP 404 (: 404)

Resource not found
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.<>c__DisplayClass10_0.<<CompleteChatStreamingAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+MoveNext()
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Controllers.PlaywrightController.RunPlaywright(PromptRequest request, CancellationToken cancellationToken) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Controllers\PlaywrightController.cs:line 93
2025-06-26 13:53:17.383 +07:00 [INF] Running legacy Playwright execution: Open tinhte forums
Open random topic and get the first comment from this topic
2025-06-26 13:53:17.393 +07:00 [INF] Starting Playwright run with prompt: Open tinhte forums
Open random topic and get the first comment from this topic
2025-06-26 13:53:17.401 +07:00 [INF] Creating chat client for provider: Azure, model: gpt-4o
2025-06-26 13:53:19.823 +07:00 [INF] MCP client successfully created
2025-06-26 13:53:19.825 +07:00 [INF] MCP client created and tools fetched
2025-06-26 13:53:20.058 +07:00 [ERR] Error during Playwright streaming response
System.ClientModel.ClientResultException: HTTP 404 (: 404)

Resource not found
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.<>c__DisplayClass10_0.<<CompleteChatStreamingAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+MoveNext()
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
2025-06-26 13:53:20.093 +07:00 [ERR] Error in legacy Playwright execution: Open tinhte forums
Open random topic and get the first comment from this topic
System.ClientModel.ClientResultException: HTTP 404 (: 404)

Resource not found
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.<>c__DisplayClass10_0.<<CompleteChatStreamingAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+MoveNext()
   at OpenAI.AsyncSseUpdateCollection`1.GetRawPagesAsync()+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+MoveNext()
   at System.ClientModel.AsyncCollectionResult`1.GetAsyncEnumerator(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.OpenAIChatClient.FromOpenAIStreamingChatCompletionAsync(IAsyncEnumerable`1 updates, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+MoveNext()
   at Microsoft.Extensions.AI.FunctionInvokingChatClient.GetStreamingResponseAsync(IEnumerable`1 messages, ChatOptions options, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Services.Automation.PlaywrightService.RunPlaywrightAsync(String prompt, CancellationToken cancellation) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Services\Automation\PlaywrightService.cs:line 25
   at Bravo.AutomationAgent.Controllers.PlaywrightController.RunPlaywright(PromptRequest request, CancellationToken cancellationToken) in C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\Controllers\PlaywrightController.cs:line 93
2025-06-26 13:53:28.555 +07:00 [WRN] Failed to determine the https port for redirect.
2025-06-26 13:53:28.782 +07:00 [INF] Running legacy Playwright execution: Open tinhte forums
Open random topic and get the first comment from this topic
2025-06-26 13:53:28.793 +07:00 [INF] Starting Playwright run with prompt: Open tinhte forums
Open random topic and get the first comment from this topic
2025-06-26 13:53:28.804 +07:00 [INF] Creating chat client for provider: Azure, model: gpt-4o
2025-06-26 13:53:30.723 +07:00 [INF] MCP client successfully created
2025-06-26 13:53:30.724 +07:00 [INF] MCP client created and tools fetched
2025-06-26 13:53:48.219 +07:00 [INF] Received 127 updates from streaming response
2025-06-26 13:53:48.225 +07:00 [INF] Processing 6 messages for result handling
2025-06-26 13:53:48.310 +07:00 [INF] Result processing complete. Status: Passed, Steps: 2
2025-06-26 13:53:48.314 +07:00 [INF] Legacy execution completed
2025-06-26 13:57:38.891 +07:00 [INF] Executing test case: Open tinhte forums
Open random topic and get the first comment from this topic
Verify that the first comment is blank
2025-06-26 13:57:38.905 +07:00 [INF] Starting test execution for: Open tinhte forums
Open random topic and get the first comment from this topic
Verify that the first comment is blank
2025-06-26 13:57:38.914 +07:00 [WRN] System prompts file not found at: C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\bin\Debug\net9.0\Configuration\SystemPrompts.md
2025-06-26 13:57:38.925 +07:00 [INF] Creating chat client for provider: Azure, model: gpt-4o
2025-06-26 13:57:40.994 +07:00 [INF] MCP client successfully created
2025-06-26 13:57:41.001 +07:00 [INF] MCP client created with 25 tools available
2025-06-26 13:58:09.003 +07:00 [INF] Test execution completed in 27998.5326ms with 517 updates
2025-06-26 13:58:09.025 +07:00 [INF] Processing 10 messages for result handling
2025-06-26 13:58:09.040 +07:00 [INF] Result processing complete. Status: Passed, Steps: 5
2025-06-26 13:58:09.057 +07:00 [INF] Test execution completed with status: Passed
2025-06-26 14:07:26.679 +07:00 [WRN] Failed to determine the https port for redirect.
2025-06-26 14:07:26.800 +07:00 [INF] Executing test case: Open tinhte forums
Open random topic and get the first comment from this topic
Verify that the first comment is blank
2025-06-26 14:07:26.804 +07:00 [INF] Starting test execution for: Open tinhte forums
Open random topic and get the first comment from this topic
Verify that the first comment is blank
2025-06-26 14:07:26.808 +07:00 [INF] System prompts loaded successfully from: C:\Users\<USER>\Desktop\workspace\Bravo.AutomationAi\Bravo.AutomationAgent\bin\Debug\net9.0\Configuration\SystemPrompts.md
2025-06-26 14:07:26.821 +07:00 [INF] Creating chat client for provider: Azure, model: gpt-4o
2025-06-26 14:07:28.898 +07:00 [INF] MCP client successfully created
2025-06-26 14:07:28.927 +07:00 [INF] MCP client created with 25 tools available
2025-06-26 14:07:46.668 +07:00 [INF] Test execution completed in 17734.9269ms with 443 updates
2025-06-26 14:07:46.676 +07:00 [INF] Processing 8 messages for result handling
2025-06-26 14:07:46.713 +07:00 [INF] Result processing complete. Status: Passed, Steps: 4
2025-06-26 14:07:46.717 +07:00 [INF] Test execution completed with status: Passed
