using Microsoft.Extensions.AI;
using System.Text.Json.Serialization;

namespace Bravo.AutomationAgent.Models.Responses;

public class MissionResult
{
    [JsonPropertyName("userPromt")]
    public string UserPrompt { get; set; } = null!;

    [JsonPropertyName("status")]
    public string Status { get; set; } = "Passed";

    [JsonPropertyName("duration")]
    public int Duration { get; set; }

    [JsonPropertyName("steps")]
    public List<TestStep> Steps { get; set; } = [];

    [JsonPropertyName("record")]
    public string? Record { get; set; }

    // Internal properties for processing
    [JsonIgnore]
    public string Id { get; set; } = Guid.NewGuid().ToString();

    [JsonIgnore]
    public IList<ChatMessage> Messages { get; set; } = [];
}

public class TestStep
{
    [JsonPropertyName("step")]
    public string Step { get; set; } = null!;

    [JsonPropertyName("status")]
    public string Status { get; set; } = "Passed";

    [JsonPropertyName("duration")]
    public int Duration { get; set; }

    [JsonPropertyName("Error")]
    public string? Error { get; set; }

    [JsonPropertyName("Sreenshot")]
    public string? Screenshot { get; set; }
}