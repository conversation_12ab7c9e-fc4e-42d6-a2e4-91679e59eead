using Microsoft.Extensions.AI;
using System.Text.Json.Serialization;

namespace Bravo.AutomationAgent.Models.Responses;

public class TestExecutionResult
{
    [JsonPropertyName("testName")]
    public string TestName { get; set; } = null!;

    [JsonPropertyName("status")]
    public string Status { get; set; } = "Passed";

    [JsonPropertyName("duration")]
    public int Duration { get; set; }

    [JsonPropertyName("environment")]
    public string Environment { get; set; } = null!;

    [JsonPropertyName("testId")]
    public string TestId { get; set; } = null!;

    [JsonPropertyName("precondition")]
    public string? Precondition { get; set; }

    [JsonPropertyName("steps")]
    public List<TestStep> Steps { get; set; } = [];

    [JsonPropertyName("logFile")]
    public string LogFile { get; set; } = "log.txt";

    [JsonPropertyName("screenshotFiles")]
    public List<string> ScreenshotFiles { get; set; } = [];

    [JsonPropertyName("videoFile")]
    public string? VideoFile { get; set; }

    [JsonPropertyName("traceFile")]
    public string? TraceFile { get; set; }

    // Internal properties for processing
    [JsonIgnore]
    public IList<ChatMessage> Messages { get; set; } = [];

    [JsonIgnore]
    public string ArtifactPath { get; set; } = null!;
}

public class TestStep
{
    [JsonPropertyName("step")]
    public string Step { get; set; } = null!;

    [JsonPropertyName("status")]
    public string Status { get; set; } = "Passed";

    [JsonPropertyName("duration")]
    public int Duration { get; set; }

    [JsonPropertyName("error")]
    public string? Error { get; set; }

    [JsonPropertyName("screenshot")]
    public string? Screenshot { get; set; }
}