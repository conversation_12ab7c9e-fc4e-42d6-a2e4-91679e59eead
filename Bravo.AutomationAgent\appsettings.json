{"AllowedHosts": "*", "Chat": {"Provider": "Azure", "Model": "gpt-4o"}, "OpenAI": {"ApiKey": "********************************************************************************************************************************************************************", "Model": "gpt-4o-mini"}, "Azure": {"ApiKey": "CclvK1We0cAXoyS3F3zkmYE985vYj3na4qmrWsY4YIrwSM1j2Q87JQQJ99BEACfhMk5XJ3w3AAABACOG6lKO", "Endpoint": "https://ai-up-bravo-dev.openai.azure.com/", "Model": "gpt-4o"}, "Gemini": {"ApiKey": "AIzaSyDvxFESliCpQtyHWwLyQc0g-hGYP9wPPL8", "Model": "gemini-2.0-flash-thinking-exp-01-21"}, "TestExecution": {"DefaultTimeout": 30000, "MaxTimeout": 300000, "RetryAttempts": 3, "EnableScreenshots": true, "EnableTracing": false, "SoftAssertions": true}, "Screenshots": {"Path": "screenshots", "Format": "png", "Quality": 90, "FullPage": true}, "Traces": {"Path": "traces", "EnableVideo": false, "EnableScreenshots": true, "RetentionDays": 7}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "Logs/log-.txt", "rollingInterval": "Day"}}]}}