using Bravo.AutomationAgent.Models.Responses;
using Microsoft.Extensions.AI;
using System.Collections.Generic;

namespace Bravo.AutomationAgent.Services.ResultHandlers;

public class PlaywrightResultHandler : IPlaywrightResultHandler
{
    public MissionResult Handle(IList<ChatMessage> messages)
    {
        string userRequest = "";
        var steps = new List<string>();
        foreach (var msg in messages)
        {
            if (msg.Role == ChatRole.User && string.IsNullOrEmpty(userRequest))
            {
                foreach (var content in msg.Contents)
                {
                    var type = content.GetType();
                    var typeName = type.Name;
                    if (typeName.Contains("Text", System.StringComparison.OrdinalIgnoreCase))
                    {
                        var textProp = type.GetProperty("Text");
                        if (textProp != null)
                        {
                            var text = textProp.GetValue(content) as string;
                            if (!string.IsNullOrEmpty(text))
                            {
                                userRequest = text;
                                break;
                            }
                        }
                    }
                }
            }
            else if (msg.Role == ChatRole.Assistant)
            {
                foreach (var content in msg.Contents)
                {
                    var type = content.GetType();
                    var typeName = type.Name;
                    if (typeName.Contains("Text", System.StringComparison.OrdinalIgnoreCase))
                    {
                        var textProp = type.GetProperty("Text");
                        if (textProp != null)
                        {
                            var text = textProp.GetValue(content) as string;
                            if (!string.IsNullOrEmpty(text))
                                steps.Add(text);
                        }
                    }
                    else if (typeName.Contains("FunctionCall", System.StringComparison.OrdinalIgnoreCase))
                    {
                        var nameProp = type.GetProperty("Name");
                        var argsProp = type.GetProperty("Arguments");
                        var name = nameProp?.GetValue(content) as string ?? "unknown";
                        var args = argsProp?.GetValue(content);
                        var argsJson = args != null ? System.Text.Json.JsonSerializer.Serialize(args) : "{}";
                        steps.Add($"FunctionCall: {name}({argsJson})");
                    }
                }
            }
        }
        return new MissionResult
        {
            UserRequest = userRequest,
            Steps = steps,
            Messages = messages
        };
    }
}