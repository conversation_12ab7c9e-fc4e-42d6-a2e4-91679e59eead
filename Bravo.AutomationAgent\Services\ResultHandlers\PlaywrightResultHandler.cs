using Bravo.AutomationAgent.Models.Responses;
using Microsoft.Extensions.AI;
using System.Collections.Generic;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace Bravo.AutomationAgent.Services.ResultHandlers;

public class PlaywrightResultHandler(ILogger<PlaywrightResultHandler> logger) : IPlaywrightResultHandler
{
    private readonly Dictionary<string, int> _stepTimings = new();
    private DateTime _executionStart = DateTime.UtcNow;

    public MissionResult Handle(IList<ChatMessage> messages)
    {
        logger.LogInformation("Processing {MessageCount} messages for result handling", messages.Count);

        var userRequest = ExtractUserRequest(messages);
        var testSteps = ProcessMessages(messages);
        var overallStatus = DetermineOverallStatus(testSteps);
        var totalDuration = CalculateTotalDuration(testSteps);

        var result = new MissionResult
        {
            UserPrompt = userRequest,
            Status = overallStatus,
            Duration = totalDuration,
            Steps = testSteps,
            Messages = messages
        };

        if (overallStatus == "Failed")
        {
            result.Record = GenerateRecordPath();
        }

        logger.LogInformation("Result processing complete. Status: {Status}, Steps: {StepCount}",
            overallStatus, testSteps.Count);

        return result;
    }

    private string ExtractUserRequest(IList<ChatMessage> messages)
    {
        foreach (var msg in messages)
        {
            if (msg.Role == ChatRole.User)
            {
                foreach (var content in msg.Contents)
                {
                    var text = ExtractTextFromContent(content);
                    if (!string.IsNullOrEmpty(text))
                    {
                        return text;
                    }
                }
            }
        }
        return "Unknown request";
    }

    private List<TestStep> ProcessMessages(IList<ChatMessage> messages)
    {
        var steps = new List<TestStep>();
        var stepCounter = 1;

        foreach (var msg in messages)
        {
            if (msg.Role == ChatRole.Assistant)
            {
                steps.AddRange(ProcessAssistantMessage(msg, ref stepCounter));
            }
            else if (msg.Role == ChatRole.Tool)
            {
                steps.AddRange(ProcessToolMessage(msg, ref stepCounter));
            }
        }

        return steps;
    }

    private List<TestStep> ProcessAssistantMessage(ChatMessage message, ref int stepCounter)
    {
        var steps = new List<TestStep>();

        foreach (var content in message.Contents)
        {
            var type = content.GetType();
            var typeName = type.Name;

            if (typeName.Contains("FunctionCall", StringComparison.OrdinalIgnoreCase))
            {
                var step = ProcessFunctionCall(content, stepCounter++);
                if (step != null)
                    steps.Add(step);
            }
            else if (typeName.Contains("Text", StringComparison.OrdinalIgnoreCase))
            {
                var text = ExtractTextFromContent(content);
                if (!string.IsNullOrEmpty(text) && IsActionDescription(text))
                {
                    steps.Add(new TestStep
                    {
                        Step = text,
                        Status = "Passed",
                        Duration = EstimateStepDuration(text)
                    });
                    stepCounter++;
                }
            }
        }

        return steps;
    }

    private List<TestStep> ProcessToolMessage(ChatMessage message, ref int stepCounter)
    {
        var steps = new List<TestStep>();

        foreach (var content in message.Contents)
        {
            var text = ExtractTextFromContent(content);
            if (!string.IsNullOrEmpty(text))
            {
                var step = ParseToolResponse(text, stepCounter++);
                if (step != null)
                    steps.Add(step);
            }
        }

        return steps;
    }

    private TestStep? ProcessFunctionCall(object content, int stepNumber)
    {
        try
        {
            var type = content.GetType();
            var nameProp = type.GetProperty("Name");
            var argsProp = type.GetProperty("Arguments");

            var name = nameProp?.GetValue(content) as string ?? "unknown";
            var args = argsProp?.GetValue(content);

            var stepDescription = FormatFunctionCallDescription(name, args);

            return new TestStep
            {
                Step = stepDescription,
                Status = "Passed",
                Duration = EstimateStepDuration(stepDescription)
            };
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Failed to process function call content");
            return null;
        }
    }

    private string FormatFunctionCallDescription(string functionName, object? args)
    {
        try
        {
            var argsJson = args != null ? JsonSerializer.Serialize(args) : "{}";
            var argsDict = JsonSerializer.Deserialize<Dictionary<string, object>>(argsJson);

            return functionName.ToLowerInvariant() switch
            {
                "navigate" => $"Navigate to URL: {GetArgValue(argsDict, "url")}",
                "click" => $"Click element: {GetArgValue(argsDict, "selector")}",
                "fill" => $"Fill input '{GetArgValue(argsDict, "selector")}' with value: {GetArgValue(argsDict, "value")}",
                "type" => $"Type text '{GetArgValue(argsDict, "text")}' into: {GetArgValue(argsDict, "selector")}",
                "wait_for_selector" => $"Wait for element: {GetArgValue(argsDict, "selector")}",
                "screenshot" => $"Take screenshot: {GetArgValue(argsDict, "path")}",
                "get_text" => $"Get text from element: {GetArgValue(argsDict, "selector")}",
                "assert_text" => $"Verify text '{GetArgValue(argsDict, "expected")}' in element: {GetArgValue(argsDict, "selector")}",
                _ => $"Execute {functionName} with parameters: {argsJson}"
            };
        }
        catch
        {
            return $"Execute {functionName}";
        }
    }

    private string GetArgValue(Dictionary<string, object>? args, string key)
    {
        if (args?.TryGetValue(key, out var value) == true)
        {
            return value?.ToString() ?? "";
        }
        return "";
    }

    private TestStep? ParseToolResponse(string response, int stepNumber)
    {
        try
        {
            // Try to parse as JSON first
            if (response.TrimStart().StartsWith("{"))
            {
                var jsonResponse = JsonSerializer.Deserialize<Dictionary<string, object>>(response);
                return jsonResponse != null ? ParseJsonToolResponse(jsonResponse, stepNumber) : null;
            }

            // Parse as plain text response
            return ParseTextToolResponse(response, stepNumber);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Failed to parse tool response: {Response}", response);
            return new TestStep
            {
                Step = $"Tool response: {response}",
                Status = "Failed",
                Duration = 100,
                Error = "Failed to parse tool response"
            };
        }
    }

    private TestStep? ParseJsonToolResponse(Dictionary<string, object> jsonResponse, int stepNumber)
    {
        var status = GetJsonValue(jsonResponse, "success", "true") == "true" ? "Passed" : "Failed";
        var message = GetJsonValue(jsonResponse, "message", "Action completed");
        var error = GetJsonValue(jsonResponse, "error", null);

        return new TestStep
        {
            Step = message ?? string.Empty,
            Status = status,
            Duration = EstimateStepDuration(message ?? ""),
            Error = error
        };
    }

    private TestStep? ParseTextToolResponse(string response, int stepNumber)
    {
        var isError = response.Contains("error", StringComparison.OrdinalIgnoreCase) ||
                     response.Contains("failed", StringComparison.OrdinalIgnoreCase) ||
                     response.Contains("exception", StringComparison.OrdinalIgnoreCase);

        return new TestStep
        {
            Step = response,
            Status = isError ? "Failed" : "Passed",
            Duration = EstimateStepDuration(response),
            Error = isError ? response : null
        };
    }

    private string? GetJsonValue(Dictionary<string, object> dict, string key, string? defaultValue)
    {
        return dict.TryGetValue(key, out var value) ? value?.ToString() ?? defaultValue : defaultValue;
    }

    private string ExtractTextFromContent(object content)
    {
        try
        {
            var type = content.GetType();
            var textProp = type.GetProperty("Text");
            return textProp?.GetValue(content) as string ?? "";
        }
        catch
        {
            return "";
        }
    }

    private bool IsActionDescription(string text)
    {
        var actionKeywords = new[] { "click", "navigate", "fill", "type", "wait", "verify", "assert", "get", "take" };
        return actionKeywords.Any(keyword => text.Contains(keyword, StringComparison.OrdinalIgnoreCase));
    }

    private int EstimateStepDuration(string stepDescription)
    {
        return stepDescription.ToLowerInvariant() switch
        {
            var s when s.Contains("navigate") => 2000,
            var s when s.Contains("click") => 500,
            var s when s.Contains("fill") || s.Contains("type") => 300,
            var s when s.Contains("wait") => 1000,
            var s when s.Contains("screenshot") => 200,
            var s when s.Contains("verify") || s.Contains("assert") => 100,
            _ => 500
        };
    }

    private string DetermineOverallStatus(List<TestStep> steps)
    {
        return steps.Any(s => s.Status == "Failed") ? "Failed" : "Passed";
    }

    private int CalculateTotalDuration(List<TestStep> steps)
    {
        return steps.Sum(s => s.Duration);
    }

    private string GenerateRecordPath()
    {
        var timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
        return $"recordings/test_execution_{timestamp}";
    }
}