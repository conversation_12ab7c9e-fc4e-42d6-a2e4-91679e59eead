using Bravo.AutomationAgent.Models.Requests;
using Bravo.AutomationAgent.Models.Responses;
using Bravo.AutomationAgent.Services.Artifacts;
using Bravo.AutomationAgent.Services.Logging;
using Microsoft.Extensions.AI;
using System.Text.Json;

namespace Bravo.AutomationAgent.Services.ResultHandlers;

public class PlaywrightResultHandler(ILogger<PlaywrightResultHandler> logger) : IPlaywrightResultHandler
{
    public async Task<TestExecutionResult> HandleAsync(IList<ChatMessage> messages, TestExecutionRequest request, ITestLogger testLogger, IArtifactManager artifactManager)
    {
        logger.LogInformation("Processing {MessageCount} messages for result handling", messages.Count);
        testLogger.LogInfo("Processing test execution results");
        
        var testSteps = ProcessMessages(messages, testLogger);
        var overallStatus = DetermineOverallStatus(testSteps);
        var totalDuration = CalculateTotalDuration(testSteps);
        
        var result = new TestExecutionResult
        {
            TestName = request.Title,
            Status = overallStatus,
            Duration = totalDuration,
            Environment = request.Environment,
            TestId = request.GetOrGenerateTestId(),
            Precondition = request.Precondition,
            Steps = testSteps,
            Messages = messages,
            ArtifactPath = artifactManager.GetArtifactPath(request.Environment, request.GetOrGenerateTestId())
        };

        result.ScreenshotFiles = testSteps
            .Where(s => !string.IsNullOrEmpty(s.Screenshot))
            .Select(s => s.Screenshot!)
            .ToList();

        if (request.EnableVideo)
        {
            result.VideoFile = "video.mp4";
        }

        if (request.EnableTracing && overallStatus == "Failed")
        {
            result.TraceFile = "trace.zip";
        }

        testLogger.LogInfo("Result processing complete. Status: {Status}, Steps: {StepCount}", 
            overallStatus, testSteps.Count);

        return result;
    }

    private List<TestStep> ProcessMessages(IList<ChatMessage> messages, ITestLogger testLogger)
    {
        var steps = new List<TestStep>();
        var stepCounter = 1;

        foreach (var msg in messages)
        {
            if (msg.Role == ChatRole.Assistant)
            {
                steps.AddRange(ProcessAssistantMessage(msg, ref stepCounter, testLogger));
            }
            else if (msg.Role == ChatRole.Tool)
            {
                steps.AddRange(ProcessToolMessage(msg, ref stepCounter, testLogger));
            }
        }

        return steps;
    }

    private List<TestStep> ProcessAssistantMessage(ChatMessage message, ref int stepCounter, ITestLogger testLogger)
    {
        var steps = new List<TestStep>();

        foreach (var content in message.Contents)
        {
            var type = content.GetType();
            var typeName = type.Name;

            if (typeName.Contains("FunctionCall", StringComparison.OrdinalIgnoreCase))
            {
                var step = ProcessFunctionCall(content, stepCounter++, testLogger);
                if (step != null)
                    steps.Add(step);
            }
            else if (typeName.Contains("Text", StringComparison.OrdinalIgnoreCase))
            {
                var text = ExtractTextFromContent(content);
                if (!string.IsNullOrEmpty(text) && IsActionDescription(text))
                {
                    var step = new TestStep
                    {
                        Step = text,
                        Status = "Passed",
                        Duration = EstimateStepDuration(text)
                    };
                    
                    testLogger.LogStep(step.Step, step.Status, step.Duration);
                    steps.Add(step);
                    stepCounter++;
                }
            }
        }

        return steps;
    }

    private List<TestStep> ProcessToolMessage(ChatMessage message, ref int stepCounter, ITestLogger testLogger)
    {
        var steps = new List<TestStep>();

        foreach (var content in message.Contents)
        {
            var text = ExtractTextFromContent(content);
            if (!string.IsNullOrEmpty(text))
            {
                var step = ParseToolResponse(text, stepCounter++, testLogger);
                if (step != null)
                    steps.Add(step);
            }
        }

        return steps;
    }

    private TestStep? ProcessFunctionCall(object content, int stepNumber, ITestLogger testLogger)
    {
        try
        {
            var type = content.GetType();
            var nameProp = type.GetProperty("Name");
            var argsProp = type.GetProperty("Arguments");
            
            var name = nameProp?.GetValue(content) as string ?? "unknown";
            var args = argsProp?.GetValue(content);
            
            var stepDescription = FormatFunctionCallDescription(name, args);
            
            var step = new TestStep
            {
                Step = stepDescription,
                Status = "Passed",
                Duration = EstimateStepDuration(stepDescription)
            };
            
            testLogger.LogStep(step.Step, step.Status, step.Duration);
            return step;
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Failed to process function call content");
            return null;
        }
    }

    private string FormatFunctionCallDescription(string functionName, object? args)
    {
        try
        {
            var argsJson = args != null ? JsonSerializer.Serialize(args) : "{}";
            var argsDict = JsonSerializer.Deserialize<Dictionary<string, object>>(argsJson);
            
            return functionName.ToLowerInvariant() switch
            {
                "navigate" => $"Navigate to URL: {GetArgValue(argsDict, "url")}",
                "click" => $"Click element: {GetArgValue(argsDict, "selector")}",
                "fill" => $"Fill input '{GetArgValue(argsDict, "selector")}' with value: {GetArgValue(argsDict, "value")}",
                "type" => $"Type text '{GetArgValue(argsDict, "text")}' into: {GetArgValue(argsDict, "selector")}",
                "wait_for_selector" => $"Wait for element: {GetArgValue(argsDict, "selector")}",
                "screenshot" => $"Take screenshot: {GetArgValue(argsDict, "path")}",
                "get_text" => $"Get text from element: {GetArgValue(argsDict, "selector")}",
                "assert_text" => $"Verify text '{GetArgValue(argsDict, "expected")}' in element: {GetArgValue(argsDict, "selector")}",
                _ => $"Execute {functionName}"
            };
        }
        catch
        {
            return $"Execute {functionName}";
        }
    }

    private string GetArgValue(Dictionary<string, object>? args, string key)
    {
        return args?.TryGetValue(key, out var value) == true ? value?.ToString() ?? "" : "";
    }

    private TestStep? ParseToolResponse(string response, int stepNumber, ITestLogger testLogger)
    {
        try
        {
            if (response.TrimStart().StartsWith("{"))
            {
                var jsonResponse = JsonSerializer.Deserialize<Dictionary<string, object>>(response);
                return jsonResponse != null ? ParseJsonToolResponse(jsonResponse, stepNumber, testLogger) : null;
            }
            
            return ParseTextToolResponse(response, stepNumber, testLogger);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Failed to parse tool response: {Response}", response);
            var step = new TestStep
            {
                Step = $"Tool response: {response}",
                Status = "Failed",
                Duration = 100,
                Error = "Failed to parse tool response"
            };
            
            testLogger.LogStep(step.Step, step.Status, step.Duration, step.Error);
            return step;
        }
    }

    private TestStep? ParseJsonToolResponse(Dictionary<string, object> jsonResponse, int stepNumber, ITestLogger testLogger)
    {
        var status = GetJsonValue(jsonResponse, "success", "true") == "true" ? "Passed" : "Failed";
        var message = GetJsonValue(jsonResponse, "message", "Action completed");
        var error = GetJsonValue(jsonResponse, "error", null);
        
        var step = new TestStep
        {
            Step = message,
            Status = status,
            Duration = EstimateStepDuration(message),
            Error = error
        };
        
        testLogger.LogStep(step.Step, step.Status, step.Duration, step.Error);
        return step;
    }

    private TestStep? ParseTextToolResponse(string response, int stepNumber, ITestLogger testLogger)
    {
        var isError = response.Contains("error", StringComparison.OrdinalIgnoreCase) ||
                     response.Contains("failed", StringComparison.OrdinalIgnoreCase) ||
                     response.Contains("exception", StringComparison.OrdinalIgnoreCase);

        var step = new TestStep
        {
            Step = response,
            Status = isError ? "Failed" : "Passed",
            Duration = EstimateStepDuration(response),
            Error = isError ? response : null
        };
        
        testLogger.LogStep(step.Step, step.Status, step.Duration, step.Error);
        return step;
    }

    private string GetJsonValue(Dictionary<string, object> dict, string key, string? defaultValue)
    {
        return dict.TryGetValue(key, out var value) ? value?.ToString() ?? defaultValue : defaultValue;
    }

    private string ExtractTextFromContent(object content)
    {
        try
        {
            var type = content.GetType();
            var textProp = type.GetProperty("Text");
            return textProp?.GetValue(content) as string ?? "";
        }
        catch
        {
            return "";
        }
    }

    private bool IsActionDescription(string text)
    {
        var actionKeywords = new[] { "click", "navigate", "fill", "type", "wait", "verify", "assert", "get", "take" };
        return actionKeywords.Any(keyword => text.Contains(keyword, StringComparison.OrdinalIgnoreCase));
    }

    private int EstimateStepDuration(string stepDescription)
    {
        return stepDescription.ToLowerInvariant() switch
        {
            var s when s.Contains("navigate") => 2000,
            var s when s.Contains("click") => 500,
            var s when s.Contains("fill") || s.Contains("type") => 300,
            var s when s.Contains("wait") => 1000,
            var s when s.Contains("screenshot") => 200,
            var s when s.Contains("verify") || s.Contains("assert") => 100,
            _ => 500
        };
    }

    private string DetermineOverallStatus(List<TestStep> steps)
    {
        return steps.Any(s => s.Status == "Failed") ? "Failed" : "Passed";
    }

    private int CalculateTotalDuration(List<TestStep> steps)
    {
        return steps.Sum(s => s.Duration);
    }
}
