# Bravo Automation AI - API-Based Test Automation Framework

A server-side automation framework using **Playwright MCP** that receives test case definitions via API, executes them, and returns structured results.

## 🎯 Overview

This framework allows users to send **what** to test in natural language, and the system handles **how** to execute it using intelligent automation rules and Playwright MCP integration.

## 🏗️ Architecture

- **C# Backend** using ASP.NET Core
- **Playwright MCP** for browser automation
- **REST API** for test execution
- **Structured JSON** input/output
- **Built-in locator strategies** and soft assertions
- **System prompts** defined in MD files for maintainability

## 🚀 Features

### Core Capabilities

- ✅ Natural language test case execution
- ✅ Structured step-by-step results
- ✅ Screenshot capture on failures
- ✅ Trace recording for debugging
- ✅ Soft assertions (continue on failure)
- ✅ Smart locator strategies
- ✅ Comprehensive error handling

### API Endpoints

- `POST /api/playwright/execute` - Execute structured test cases
- `POST /api/playwright/run` - Legacy endpoint for backward compatibility
- `GET /api/playwright/health` - Health check

## 📋 Input Format

Send test cases as natural language descriptions:

```json
{
  "testCase": "Open tinhte forums\nOpen a random topic\nGet the first comment\nVerify the title is not blank",
  "timeout": 30000,
  "enableScreenshots": true,
  "enableTracing": false,
  "parameters": {
    "baseUrl": "https://tinhte.vn"
  },
  "userId": "test_user_001"
}
```

## 📤 Output Format

### Success Response

```json
{
  "userPromt": "Open tinhte forums\nOpen a random topic\nGet the first comment\nVerify the title is not blank",
  "status": "Passed",
  "duration": 5000,
  "steps": [
    {
      "step": "Navigate to URL: https://tinhte.vn",
      "status": "Passed",
      "duration": 2000
    },
    {
      "step": "Click element: random topic",
      "status": "Passed",
      "duration": 1500
    },
    {
      "step": "Get text from element: first comment",
      "status": "Passed",
      "duration": 1000
    },
    { "step": "Verify title is not blank", "status": "Passed", "duration": 500 }
  ]
}
```

### Error Response

```json
{
  "userPromt": "Open tinhte forums\nVerify invalid element",
  "status": "Failed",
  "record": "traces/test_execution_20250626_143022",
  "duration": 3000,
  "steps": [
    {
      "step": "Navigate to URL: https://tinhte.vn",
      "status": "Passed",
      "duration": 2000
    },
    {
      "step": "Verify invalid element",
      "status": "Failed",
      "duration": 1000,
      "Error": "Element not found",
      "Sreenshot": "screenshots/error_20250626_143025.png"
    }
  ]
}
```

## 🛠️ Built-in Rules

### Locator Strategy (Priority Order)

1. **Data Attributes**: `[data-testid]`, `[data-test]`, `[data-cy]`
2. **Semantic HTML**: `role`, `aria-label`, `aria-labelledby`
3. **Text Content**: Exact text match, then partial text match
4. **CSS Selectors**: Class names (stable ones), IDs
5. **XPath**: As last resort for complex scenarios

### Verification & Assertions

- **Soft Assertions**: Continue execution after failures
- **Built-in Validations**: Element presence, visibility, text content, attributes
- **Screenshot Capture**: Automatic on assertion failures
- **Structured Results**: Pass/fail with actual vs expected values

### Error Handling

- **Comprehensive Logging**: Detailed error messages and stack traces
- **Screenshot Capture**: Full page screenshots on failures
- **Trace Recording**: Complete execution traces for debugging
- **Recovery Strategies**: Alternative locators and retry mechanisms

## 🔧 Configuration

### appsettings.json

```json
{
  "TestExecution": {
    "DefaultTimeout": 30000,
    "MaxTimeout": 300000,
    "RetryAttempts": 3,
    "EnableScreenshots": true,
    "EnableTracing": false,
    "SoftAssertions": true
  },
  "Screenshots": {
    "Path": "screenshots",
    "Format": "png",
    "Quality": 90,
    "FullPage": true
  },
  "Traces": {
    "Path": "traces",
    "EnableVideo": false,
    "EnableScreenshots": true,
    "RetentionDays": 7
  }
}
```

### System Prompts

System behavior is defined in `Configuration/SystemPrompts.md` for easy maintenance and updates.

## 🚀 Getting Started

### Prerequisites

- .NET 9.0 or later
- Node.js (for Playwright MCP)
- OpenAI API key (or other supported AI provider)

### Installation

1. Clone the repository
2. Configure API keys in `appsettings.json`
3. Run the application:
   ```bash
   dotnet run --project Bravo.AutomationAgent
   ```

### Quick Test

```bash
curl -X POST "http://localhost:5000/api/playwright/health"
```

### Sample Test Execution

```bash
curl -X POST "http://localhost:5000/api/playwright/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "testCase": "Navigate to https://example.com\nVerify page title contains Example",
    "timeout": 15000,
    "enableScreenshots": true
  }'
```

## 📁 Project Structure

```
Bravo.AutomationAgent/
├── Controllers/           # API controllers
├── Models/               # Request/response models
├── Services/             # Core business logic
│   ├── Automation/       # Test execution engine
│   ├── Configuration/    # System prompt management
│   ├── ErrorHandling/    # Error handling and screenshots
│   ├── ResultHandlers/   # Result processing
│   └── Validation/       # Input validation
├── Configuration/        # System prompts and rules
├── Testing/             # Integration testing helpers
└── SampleTestCases/     # Example test cases
```

## 🧪 Testing

### Run Sample Tests

```bash
curl -X POST "http://localhost:5000/api/playwright/test-samples"
```

### Integration Tests

The framework includes built-in integration testing capabilities with sample test cases for validation.

## 🔍 Monitoring

- **Structured Logging**: Comprehensive logging with Serilog
- **Health Checks**: Built-in health monitoring
- **Performance Metrics**: Execution timing and step-level performance
- **Error Tracking**: Detailed error reporting with screenshots and traces

## 📝 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📞 Support

For questions and support, please create an issue in the repository.
