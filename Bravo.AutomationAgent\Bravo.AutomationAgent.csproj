<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Azure.AI.OpenAI" Version="2.2.0-beta.4" />
        <PackageReference Include="Azure.Identity" Version="1.14.1" />
        <PackageReference Include="GeminiDotnet.Extensions.AI" Version="0.12.0" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.1" />
        <PackageReference Include="Microsoft.Extensions.AI" Version="9.6.0" />
        <PackageReference Include="Microsoft.Extensions.AI.OpenAI" Version="9.6.0-preview.1.25310.2" />
        <PackageReference Include="ModelContextProtocol" Version="0.3.0-preview.1" />
        <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.1" />
    </ItemGroup>

    <ItemGroup>
      <None Update="Configuration\SystemPrompts.md">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
    </ItemGroup>


</Project>
