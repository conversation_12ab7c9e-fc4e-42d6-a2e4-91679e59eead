using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;

namespace Bravo.AutomationAgent.Models.Requests;

public class TestExecutionRequest
{
    [Required]
    [JsonPropertyName("title")]
    public string Title { get; set; } = null!;

    [JsonPropertyName("precondition")]
    public string? Precondition { get; set; }

    [Required]
    [JsonPropertyName("content")]
    public string Content { get; set; } = null!;

    [Required]
    [JsonPropertyName("environment")]
    public string Environment { get; set; } = "test";

    [JsonPropertyName("testId")]
    public string? TestId { get; set; }

    [JsonPropertyName("timeout")]
    public int? Timeout { get; set; } = 30000;

    [JsonPropertyName("enableScreenshots")]
    public bool EnableScreenshots { get; set; } = true;

    [JsonPropertyName("enableTracing")]
    public bool EnableTracing { get; set; } = false;

    [JsonPropertyName("enableVideo")]
    public bool EnableVideo { get; set; } = false;

    public string GetOrGenerateTestId()
    {
        if (!string.IsNullOrEmpty(TestId))
            return TestId;

        // Generate from title: lowercase, replace spaces with -, remove special characters
        var generated = Title.ToLowerInvariant();
        generated = Regex.Replace(generated, @"[^a-z0-9\s-]", "");
        generated = Regex.Replace(generated, @"\s+", "-");
        generated = generated.Trim('-');

        return string.IsNullOrEmpty(generated) ? Guid.NewGuid().ToString("N")[..8] : generated;
    }
}
