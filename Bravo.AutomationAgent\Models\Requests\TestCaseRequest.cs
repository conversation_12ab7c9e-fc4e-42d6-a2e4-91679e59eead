using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Bravo.AutomationAgent.Models.Requests;

public class TestCaseRequest
{
    [Required]
    [JsonPropertyName("testCase")]
    public string TestCase { get; set; } = null!;
    
    [JsonPropertyName("parameters")]
    public Dictionary<string, string>? Parameters { get; set; }
    
    [JsonPropertyName("userId")]
    public string? UserId { get; set; }
    
    [JsonPropertyName("timeout")]
    public int? Timeout { get; set; } = 30000; // Default 30 seconds
    
    [JsonPropertyName("enableScreenshots")]
    public bool EnableScreenshots { get; set; } = true;
    
    [JsonPropertyName("enableTracing")]
    public bool EnableTracing { get; set; } = false;
}
