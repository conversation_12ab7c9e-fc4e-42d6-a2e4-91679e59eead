using Bravo.AutomationAgent.Infrastructure;
using Bravo.AutomationAgent.Models.Requests;
using Bravo.AutomationAgent.Models.Responses;
using Bravo.AutomationAgent.Services.Configuration;
using Bravo.AutomationAgent.Services.ResultHandlers;
using Microsoft.Extensions.AI;
using ModelContextProtocol.Client;

namespace Bravo.AutomationAgent.Services.Automation;

public class TestExecutionEngine(
    IChatClientFactory chatClientFactory,
    ISystemPromptService systemPromptService,
    IPlaywrightResultHandler resultHandler,
    ILogger<TestExecutionEngine> logger) : ITestExecutionEngine
{
    public async Task<MissionResult> ExecuteTestAsync(TestCaseRequest request, CancellationToken cancellationToken)
    {
        logger.LogInformation("Starting test execution for: {TestCase}", request.TestCase);

        try
        {
            var systemPrompt = await systemPromptService.GetSystemPromptAsync();
            var enhancedPrompt = await BuildEnhancedPrompt(request, systemPrompt);

            var client = chatClientFactory.CreateClient();
            var mcpClient = await GetMcpClient(cancellationToken);
            var tools = await mcpClient.ListToolsAsync(cancellationToken: cancellationToken);

            logger.LogInformation("MCP client created with {ToolCount} tools available", tools.Count);

            // Only send a single user message that includes the system prompt and test case
            var messages = new List<ChatMessage>
            {
                new(ChatRole.User, enhancedPrompt)
            };

            var updates = new List<ChatResponseUpdate>();
            var executionStart = DateTime.UtcNow;

            try
            {
                await foreach (var update in client.GetStreamingResponseAsync(
                    messages,
                    new ChatOptions
                    {
                        Tools = [.. tools],
                        Temperature = 0.1f
                    },
                    cancellationToken))
                {
                    updates.Add(update);

                    // Log progress for long-running operations
                    if (updates.Count % 10 == 0)
                    {
                        logger.LogDebug("Received {UpdateCount} updates from streaming response", updates.Count);
                    }
                }

                messages.AddMessages(updates);
                var executionTime = DateTime.UtcNow - executionStart;

                logger.LogInformation("Test execution completed in {Duration}ms with {UpdateCount} updates",
                    executionTime.TotalMilliseconds, updates.Count);

                var result = resultHandler.Handle(messages);

                // Apply post-processing based on request settings
                ApplyRequestSettings(result, request);

                return result;
            }
            catch (OperationCanceledException)
            {
                logger.LogWarning("Test execution was cancelled");
                return CreateCancelledResult(request.TestCase);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error during test execution streaming");
                return CreateErrorResult(request.TestCase, ex);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to execute test case: {TestCase}", request.TestCase);
            return CreateErrorResult(request.TestCase, ex);
        }
    }

    private Task<string> BuildEnhancedPrompt(TestCaseRequest request, string systemPrompt)
    {
        var promptBuilder = new System.Text.StringBuilder();

        promptBuilder.AppendLine("Execute the following test case with structured step-by-step reporting:");
        promptBuilder.AppendLine();
        promptBuilder.AppendLine($"TEST CASE: {request.TestCase}");
        promptBuilder.AppendLine();

        if (request.Parameters?.Any() == true)
        {
            promptBuilder.AppendLine("PARAMETERS:");
            foreach (var param in request.Parameters)
            {
                promptBuilder.AppendLine($"- {param.Key}: {param.Value}");
            }
            promptBuilder.AppendLine();
        }

        promptBuilder.AppendLine("EXECUTION REQUIREMENTS:");
        promptBuilder.AppendLine("1. Follow the system prompt rules for locator strategies and error handling");
        promptBuilder.AppendLine("2. Provide detailed step descriptions for each action performed");
        promptBuilder.AppendLine("3. Use soft assertions - continue execution even after failures when possible");
        promptBuilder.AppendLine("4. Capture screenshots on assertion failures");
        promptBuilder.AppendLine("5. Return structured results with timing information");

        if (request.Timeout.HasValue)
        {
            promptBuilder.AppendLine($"6. Respect timeout limit of {request.Timeout.Value}ms");
        }

        promptBuilder.AppendLine();
        promptBuilder.AppendLine("Begin test execution now.");

        return Task.FromResult(promptBuilder.ToString());
    }

    private async Task<IMcpClient> GetMcpClient(CancellationToken cancellationToken)
    {
        try
        {
            var mcpClient = await McpClientFactory.CreateAsync(
                new StdioClientTransport(new StdioClientTransportOptions
                {
                    Command = "npx",
                    Arguments = ["@playwright/mcp@latest", "--isolated"],
                    Name = $"Playwright Mcp {Guid.NewGuid()}"
                }),
                cancellationToken: cancellationToken);

            logger.LogInformation("MCP client successfully created");
            return mcpClient;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to create MCP client");
            throw;
        }
    }

    private void ApplyRequestSettings(MissionResult result, TestCaseRequest request)
    {
        // Apply user ID if provided
        if (!string.IsNullOrEmpty(request.UserId))
        {
            result.Id = $"{request.UserId}_{result.Id}";
        }

        // Filter out screenshots if disabled
        if (!request.EnableScreenshots)
        {
            foreach (var step in result.Steps)
            {
                step.Screenshot = null;
            }
        }

        // Remove trace recording if disabled
        if (!request.EnableTracing && result.Status == "Failed")
        {
            result.Record = null;
        }
    }

    private MissionResult CreateCancelledResult(string testCase)
    {
        return new MissionResult
        {
            UserPrompt = testCase,
            Status = "Failed",
            Duration = 0,
            Steps = [new TestStep
            {
                Step = "Test execution was cancelled",
                Status = "Failed",
                Duration = 0,
                Error = "Operation was cancelled due to timeout or user request"
            }]
        };
    }

    private MissionResult CreateErrorResult(string testCase, Exception ex)
    {
        return new MissionResult
        {
            UserPrompt = testCase,
            Status = "Failed",
            Duration = 0,
            Steps = [new TestStep
            {
                Step = "Test execution failed",
                Status = "Failed",
                Duration = 0,
                Error = $"Execution error: {ex.Message}"
            }]
        };
    }
}
