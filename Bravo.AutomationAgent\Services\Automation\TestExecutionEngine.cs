using Bravo.AutomationAgent.Infrastructure;
using Bravo.AutomationAgent.Models.Requests;
using Bravo.AutomationAgent.Models.Responses;
using Bravo.AutomationAgent.Services.Artifacts;
using Bravo.AutomationAgent.Services.Configuration;
using Bravo.AutomationAgent.Services.Logging;
using Bravo.AutomationAgent.Services.ResultHandlers;
using Microsoft.Extensions.AI;
using ModelContextProtocol.Client;

namespace Bravo.AutomationAgent.Services.Automation;

public class TestExecutionEngine(
    IChatClientFactory chatClientFactory,
    ISystemPromptService systemPromptService,
    IPlaywrightResultHandler resultHandler,
    IArtifactManager artifactManager,
    ILogger<TestExecutionEngine> logger) : ITestExecutionEngine
{
    public async Task<TestExecutionResult> ExecuteTestAsync(TestExecutionRequest request, CancellationToken cancellationToken)
    {
        var testId = request.GetOrGenerateTestId();
        var testLogger = new TestLogger();
        
        try
        {
            testLogger.Initialize(request.Environment, testId);
            var artifactPath = artifactManager.CreateTestDirectory(request.Environment, testId);
            
            testLogger.LogInfo("Starting test execution");
            testLogger.LogInfo("Title: {Title}", request.Title);
            testLogger.LogInfo("Environment: {Environment}", request.Environment);
            testLogger.LogInfo("Test ID: {TestId}", testId);
            
            var systemPrompt = await systemPromptService.GetSystemPromptAsync();
            var enhancedPrompt = BuildEnhancedPrompt(request, systemPrompt);
            
            testLogger.LogInfo("System prompt loaded, length: {Length} characters", systemPrompt.Length);
            
            var client = chatClientFactory.CreateClient();
            var mcpClient = await GetMcpClient(cancellationToken);
            var tools = await mcpClient.ListToolsAsync(cancellationToken: cancellationToken);
            
            testLogger.LogInfo("MCP client created with {ToolCount} tools available", tools.Count);
            
            var messages = new List<ChatMessage>
            {
                new(ChatRole.User, enhancedPrompt)
            };
            
            var updates = new List<ChatResponseUpdate>();
            var executionStart = DateTime.UtcNow;
            
            try
            {
                await foreach (var update in client.GetStreamingResponseAsync(
                    messages,
                    new ChatOptions
                    {
                        Tools = [.. tools],
                        Temperature = 0.1f
                    },
                    cancellationToken))
                {
                    updates.Add(update);
                    
                    if (updates.Count % 10 == 0)
                    {
                        testLogger.LogDebug("Received {UpdateCount} updates", updates.Count);
                    }
                }
                
                messages.AddMessages(updates);
                var executionTime = DateTime.UtcNow - executionStart;
                
                testLogger.LogInfo("Test execution completed in {Duration}ms with {UpdateCount} updates", 
                    executionTime.TotalMilliseconds, updates.Count);
                
                var result = await resultHandler.HandleAsync(messages, request, testLogger, artifactManager);
                
                await SaveTestArtifacts(result, testLogger);
                
                return result;
            }
            catch (OperationCanceledException)
            {
                testLogger.LogWarning("Test execution was cancelled");
                return await CreateCancelledResult(request, testLogger);
            }
            catch (Exception ex)
            {
                testLogger.LogError(ex, "Error during test execution streaming");
                return await CreateErrorResult(request, ex, testLogger);
            }
        }
        catch (Exception ex)
        {
            testLogger.LogError(ex, "Failed to execute test case: {Title}", request.Title);
            return await CreateErrorResult(request, ex, testLogger);
        }
        finally
        {
            testLogger.Dispose();
        }
    }

    private string BuildEnhancedPrompt(TestExecutionRequest request, string systemPrompt)
    {
        var promptBuilder = new System.Text.StringBuilder();
        
        promptBuilder.AppendLine(systemPrompt);
        promptBuilder.AppendLine();
        promptBuilder.AppendLine("Execute the following test case with structured step-by-step reporting:");
        promptBuilder.AppendLine();
        promptBuilder.AppendLine($"TEST TITLE: {request.Title}");
        
        if (!string.IsNullOrEmpty(request.Precondition))
        {
            promptBuilder.AppendLine($"PRECONDITION: {request.Precondition}");
        }
        
        promptBuilder.AppendLine($"TEST CONTENT: {request.Content}");
        promptBuilder.AppendLine();
        
        promptBuilder.AppendLine("EXECUTION REQUIREMENTS:");
        promptBuilder.AppendLine("1. Follow the system prompt rules for locator strategies and error handling");
        promptBuilder.AppendLine("2. Provide detailed step descriptions for each action performed");
        promptBuilder.AppendLine("3. Use soft assertions - continue execution even after failures when possible");
        promptBuilder.AppendLine("4. Capture screenshots on assertion failures");
        promptBuilder.AppendLine("5. Return structured results with timing information");
        
        if (request.Timeout.HasValue)
        {
            promptBuilder.AppendLine($"6. Respect timeout limit of {request.Timeout.Value}ms");
        }
        
        promptBuilder.AppendLine();
        promptBuilder.AppendLine("Begin test execution now.");
        
        return promptBuilder.ToString();
    }

    private async Task<IMcpClient> GetMcpClient(CancellationToken cancellationToken)
    {
        try
        {
            var mcpClient = await McpClientFactory.CreateAsync(
                new StdioClientTransport(new StdioClientTransportOptions
                {
                    Command = "npx",
                    Arguments = ["@playwright/mcp@latest", "--isolated", ""],
                    Name = $"Playwright Mcp {Guid.NewGuid()}"
                }), 
                cancellationToken: cancellationToken);
            
            logger.LogInformation("MCP client successfully created");
            return mcpClient;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to create MCP client");
            throw;
        }
    }

    private async Task SaveTestArtifacts(TestExecutionResult result, TestLogger testLogger)
    {
        try
        {
            var logContent = await testLogger.GetLogContentAsync();
            await artifactManager.SaveLogAsync(result.Environment, result.TestId, logContent);
            await artifactManager.SaveResultAsync(result.Environment, result.TestId, result);
            
            logger.LogInformation("Test artifacts saved for {TestId}", result.TestId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to save test artifacts for {TestId}", result.TestId);
        }
    }

    private async Task<TestExecutionResult> CreateCancelledResult(TestExecutionRequest request, TestLogger testLogger)
    {
        var result = new TestExecutionResult
        {
            TestName = request.Title,
            Status = "Failed",
            Duration = 0,
            Environment = request.Environment,
            TestId = request.GetOrGenerateTestId(),
            Precondition = request.Precondition,
            Steps = [new TestStep
            {
                Step = "Test execution was cancelled",
                Status = "Failed",
                Duration = 0,
                Error = "Operation was cancelled due to timeout or user request"
            }],
            ArtifactPath = artifactManager.GetArtifactPath(request.Environment, request.GetOrGenerateTestId())
        };

        await SaveTestArtifacts(result, testLogger);
        return result;
    }

    private async Task<TestExecutionResult> CreateErrorResult(TestExecutionRequest request, Exception ex, TestLogger testLogger)
    {
        var result = new TestExecutionResult
        {
            TestName = request.Title,
            Status = "Failed",
            Duration = 0,
            Environment = request.Environment,
            TestId = request.GetOrGenerateTestId(),
            Precondition = request.Precondition,
            Steps = [new TestStep
            {
                Step = "Test execution failed",
                Status = "Failed",
                Duration = 0,
                Error = $"Execution error: {ex.Message}"
            }],
            ArtifactPath = artifactManager.GetArtifactPath(request.Environment, request.GetOrGenerateTestId())
        };

        await SaveTestArtifacts(result, testLogger);
        return result;
    }
}
