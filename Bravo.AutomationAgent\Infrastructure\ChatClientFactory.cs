using Azure;
using Azure.AI.OpenAI;
using GeminiDotnet;
using GeminiDotnet.Extensions.AI;
using Microsoft.Extensions.AI;
using OpenAI;

namespace Bravo.AutomationAgent.Infrastructure;

public class ChatClientFactory(IConfiguration config, ILogger<ChatClientFactory> logger) : IChatClientFactory
{
    public IChatClient CreateClient(string? provider = null, string? model = null)
    {
        provider ??= config["Chat:Provider"] ?? "OpenAI";
        model ??= config["Chat:Model"] ?? "gpt-4o-mini";
        logger.LogInformation("Creating chat client for provider: {Provider}, model: {Model}", provider, model);
        switch (provider.ToLowerInvariant())
        {
            case "openai":
                var openAiKey = config["OpenAI:ApiKey"];
                if (string.IsNullOrWhiteSpace(openAiKey))
                    throw new InvalidOperationException("OpenAI API key is missing in configuration.");
                var openAiClient = new OpenAIClient(openAiKey);
                var chatClient = openAiClient.GetChatClient(model).AsIChatClient();
                return new ChatClientBuilder(chatClient).UseFunctionInvocation().Build();

            case "azure":
                var azureKey = config["Azure:ApiKey"] ?? string.Empty;
                var endpoint = config["Azure:Endpoint"] ?? string.Empty;
                if (string.IsNullOrWhiteSpace(azureKey) || string.IsNullOrWhiteSpace(endpoint))
                    throw new InvalidOperationException("Azure API key or endpoint is missing in configuration.");
                return new ChatClientBuilder(
                        new AzureOpenAIClient(new Uri(endpoint),
                                new AzureKeyCredential(azureKey))
                            .GetChatClient(model).AsIChatClient())
                    .UseFunctionInvocation()
                    .Build();

            case "gemini":
                var geminiKey = config["Gemini:ApiKey"] ?? string.Empty;
                if (string.IsNullOrWhiteSpace(geminiKey))
                    throw new InvalidOperationException("Gemini API key is missing in configuration.");
                var options = new GeminiClientOptions { ApiKey = geminiKey, ModelId = model };
                return new ChatClientBuilder(new GeminiChatClient(options))
                    .UseFunctionInvocation()
                    .Build();

            default:
                throw new NotSupportedException($"Provider '{provider}' is not supported.");
        }
    }
}