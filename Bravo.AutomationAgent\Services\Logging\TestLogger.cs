using System.Text;

namespace Bravo.AutomationAgent.Services.Logging;

public class TestLogger : ITestLogger, IDisposable
{
    private readonly StringBuilder _logBuffer = new();
    private readonly object _lock = new();
    private string? _logFilePath;
    private bool _disposed;

    public void Initialize(string environment, string testId)
    {
        lock (_lock)
        {
            var basePath = Path.Combine(Directory.GetCurrentDirectory(), "site", environment, testId);
            Directory.CreateDirectory(basePath);
            _logFilePath = Path.Combine(basePath, "log.txt");
            
            LogInfo("Test execution started");
            LogInfo("Environment: {Environment}", environment);
            LogInfo("Test ID: {TestId}", testId);
            LogInfo("Timestamp: {Timestamp}", DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss UTC"));
            LogInfo("----------------------------------------");
        }
    }

    public void LogInfo(string message, params object[] args)
    {
        WriteLog("INFO", message, args);
    }

    public void LogWarning(string message, params object[] args)
    {
        WriteLog("WARN", message, args);
    }

    public void LogError(string message, params object[] args)
    {
        WriteLog("ERROR", message, args);
    }

    public void LogError(Exception exception, string message, params object[] args)
    {
        WriteLog("ERROR", message, args);
        WriteLog("ERROR", "Exception: {ExceptionType}: {ExceptionMessage}", exception.GetType().Name, exception.Message);
        if (!string.IsNullOrEmpty(exception.StackTrace))
        {
            WriteLog("ERROR", "Stack Trace: {StackTrace}", exception.StackTrace);
        }
    }

    public void LogDebug(string message, params object[] args)
    {
        WriteLog("DEBUG", message, args);
    }

    public void LogStep(string stepName, string status, int duration, string? error = null)
    {
        WriteLog("STEP", "Step: {StepName} | Status: {Status} | Duration: {Duration}ms", stepName, status, duration);
        if (!string.IsNullOrEmpty(error))
        {
            WriteLog("STEP", "Error: {Error}", error);
        }
    }

    public async Task<string> GetLogContentAsync()
    {
        lock (_lock)
        {
            return _logBuffer.ToString();
        }
    }

    public async Task FlushAsync()
    {
        if (_logFilePath == null) return;

        try
        {
            string content;
            lock (_lock)
            {
                content = _logBuffer.ToString();
            }

            await File.WriteAllTextAsync(_logFilePath, content);
        }
        catch (Exception ex)
        {
            // Can't log this error to the test logger, so we'll have to ignore it
            // In a production system, you might want to log to a separate error log
            Console.WriteLine($"Failed to flush test log: {ex.Message}");
        }
    }

    private void WriteLog(string level, string message, params object[] args)
    {
        if (_disposed) return;

        try
        {
            var timestamp = DateTime.UtcNow.ToString("HH:mm:ss.fff");
            var formattedMessage = args.Length > 0 ? string.Format(message, args) : message;
            var logLine = $"[{timestamp}] [{level}] {formattedMessage}";

            lock (_lock)
            {
                _logBuffer.AppendLine(logLine);
            }
        }
        catch (Exception)
        {
            // Ignore formatting errors to prevent logging from breaking the test
        }
    }

    public void Dispose()
    {
        if (_disposed) return;

        try
        {
            FlushAsync().Wait(TimeSpan.FromSeconds(5));
        }
        catch
        {
            // Ignore errors during disposal
        }

        _disposed = true;
    }
}
