using Bravo.AutomationAgent.Models.Responses;

namespace Bravo.AutomationAgent.Services.Artifacts;

public interface IArtifactManager
{
    string CreateTestDirectory(string environment, string testId);
    Task<string> SaveLogAsync(string environment, string testId, string logContent);
    Task<string> SaveResultAsync(string environment, string testId, TestExecutionResult result);
    Task<string?> SaveScreenshotAsync(string environment, string testId, string stepName, byte[] imageData);
    Task<string?> SaveTraceAsync(string environment, string testId, byte[] traceData);
    Task<string?> SaveVideoAsync(string environment, string testId, byte[] videoData);
    string GetArtifactPath(string environment, string testId);
    Task CleanupOldArtifactsAsync(int retentionDays = 7);
}
