using Bravo.AutomationAgent.Infrastructure;
using Bravo.AutomationAgent.Services.Artifacts;
using Bravo.AutomationAgent.Services.Automation;
using Bravo.AutomationAgent.Services.Configuration;
using Bravo.AutomationAgent.Services.Logging;
using Bravo.AutomationAgent.Services.ResultHandlers;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .CreateLogger();

builder.Host.UseSerilog();

// Configure logging to write to files in site directory
builder.Services.AddSingleton<ITestLogger, TestLogger>();

// Add services to the container.
// builder.Services.AddOpenApi(); // Removed duplicate OpenAPI registration
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { Title = "Bravo Automation API", Version = "v1" });
    c.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, "Bravo.AutomationAgent.xml"), true);
});

// Register core services and factories
builder.Services.AddSingleton<IChatClientFactory, ChatClientFactory>();

// Register automation services
builder.Services.AddScoped<ITestExecutionEngine, TestExecutionEngine>();
builder.Services.AddScoped<IPlaywrightResultHandler, PlaywrightResultHandler>();

// Register configuration services
builder.Services.AddScoped<ISystemPromptService, SystemPromptService>();

// Register artifact management
builder.Services.AddScoped<IArtifactManager, ArtifactManager>();

builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
            .AllowAnyHeader()
            .AllowAnyMethod();
    });
});

var app = builder.Build();

app.UseHttpsRedirection();
app.UseCors();
app.MapControllers();
app.Run();
// Ensure to flush and close loggers on shutdown
Log.CloseAndFlush();