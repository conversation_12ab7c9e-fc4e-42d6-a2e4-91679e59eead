# Reusable Test Automation Libraries

This file contains reusable flows and custom component handling patterns for common web automation scenarios.

## Authentication Flows

### Standard Login
```
1. Navigate to login page
2. Wait for login form to be visible
3. Fill username field with provided credentials
4. Fill password field with provided credentials
5. Click login button
6. Wait for successful login (dashboard/home page load)
7. Verify login success (user menu, welcome message, or dashboard elements)
```

### OAuth/SSO Login
```
1. Navigate to login page
2. Click OAuth provider button (Google, Microsoft, etc.)
3. Handle popup/redirect to provider
4. Fill provider credentials if needed
5. Handle consent/permission screens
6. Wait for redirect back to application
7. Verify successful authentication
```

### Logout Flow
```
1. Locate user menu or profile dropdown
2. Click to open menu
3. Click logout/sign out option
4. Handle confirmation dialog if present
5. Wait for redirect to login page
6. Verify logout success
```

## Search Patterns

### Basic Search
```
1. Locate search input field
2. Clear any existing search terms
3. Type search query
4. Press Enter or click search button
5. Wait for search results to load
6. Verify results are displayed
```

### Advanced Search with Filters
```
1. Open advanced search or filters panel
2. Set search criteria (date range, category, etc.)
3. Apply filters
4. Execute search
5. Verify filtered results
6. Clear filters if needed for next test
```

## Form Handling

### Standard Form Submission
```
1. Fill all required fields
2. Validate field formats (email, phone, etc.)
3. Handle dropdowns and select lists
4. Upload files if required
5. Accept terms/conditions if present
6. Submit form
7. Handle validation errors if any
8. Verify successful submission
```

### Multi-Step Forms/Wizards
```
1. Complete first step fields
2. Click Next/Continue button
3. Verify progression to next step
4. Repeat for each step
5. Review summary if provided
6. Submit final form
7. Verify completion
```

## Date and Time Pickers

### Calendar Date Picker
```
1. Click date input field
2. Wait for calendar popup
3. Navigate to correct month/year if needed
4. Click desired date
5. Verify date is selected and popup closes
6. Confirm date appears in input field
```

### Time Picker
```
1. Click time input field
2. Set hours using dropdown or input
3. Set minutes using dropdown or input
4. Select AM/PM if 12-hour format
5. Confirm time selection
```

## Rich Text Editors

### Basic Rich Text Editing
```
1. Click in editor area to focus
2. Clear existing content if needed
3. Type or paste content
4. Apply formatting (bold, italic, etc.) if needed
5. Insert links, images, or other media if required
6. Verify content appears correctly
7. Save changes
```

### Advanced Editor Features
```
1. Use toolbar buttons for formatting
2. Handle image uploads through editor
3. Create tables or lists
4. Insert special characters or symbols
5. Switch between visual and HTML modes
6. Preview content before saving
```

## E-commerce Patterns

### Product Search and Selection
```
1. Search for product by name or category
2. Apply filters (price, brand, rating, etc.)
3. Sort results if needed
4. Select specific product from results
5. Verify product details page loads
6. Check product information (price, description, availability)
```

### Shopping Cart Operations
```
1. Add item to cart from product page
2. Verify cart count updates
3. Navigate to cart/basket page
4. Update quantities if needed
5. Remove items if required
6. Apply discount codes if available
7. Proceed to checkout
```

### Checkout Process
```
1. Review cart contents
2. Enter shipping information
3. Select shipping method
4. Enter billing information
5. Select payment method
6. Review order summary
7. Place order
8. Verify order confirmation
```

## Data Tables and Lists

### Table Interaction
```
1. Locate data table
2. Sort by column headers if needed
3. Filter table data
4. Navigate through pagination
5. Select specific rows
6. Perform bulk actions if available
7. Export data if option exists
```

### List Management
```
1. Add new items to list
2. Edit existing items
3. Delete items with confirmation
4. Reorder items if drag-drop available
5. Search within list
6. Apply filters to list view
```

## File Operations

### File Upload
```
1. Click file upload button/area
2. Handle file dialog (use setInputFiles for Playwright)
3. Select appropriate file type
4. Verify file is selected/uploaded
5. Check upload progress if shown
6. Confirm upload completion
```

### File Download
```
1. Click download link/button
2. Handle download dialog if present
3. Verify download starts
4. Check download completion
5. Verify file exists in download location
```

## Modal and Dialog Handling

### Standard Modal
```
1. Trigger modal (click button, link, etc.)
2. Wait for modal to appear
3. Verify modal content
4. Interact with modal elements
5. Close modal (X button, Cancel, or outside click)
6. Verify modal is dismissed
```

### Confirmation Dialogs
```
1. Trigger action that requires confirmation
2. Wait for confirmation dialog
3. Read confirmation message
4. Click appropriate button (OK, Cancel, Yes, No)
5. Verify action result
```

## Navigation Patterns

### Menu Navigation
```
1. Locate main navigation menu
2. Hover over menu items if dropdown
3. Click desired menu option
4. Wait for page load
5. Verify correct page is displayed
```

### Breadcrumb Navigation
```
1. Locate breadcrumb trail
2. Click desired breadcrumb level
3. Verify navigation to correct page
4. Check breadcrumb updates correctly
```

## Responsive Design Testing

### Mobile View Testing
```
1. Set viewport to mobile size
2. Verify mobile menu functionality
3. Test touch interactions
4. Check responsive layout
5. Verify all features work on mobile
```

### Cross-Browser Compatibility
```
1. Test core functionality across browsers
2. Verify consistent appearance
3. Check browser-specific features
4. Test performance across browsers
```
