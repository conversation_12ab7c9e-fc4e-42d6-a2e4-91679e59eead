namespace Bravo.AutomationAgent.Services.Configuration;

public class SystemPromptService : ISystemPromptService
{
    private readonly string _systemPromptsPath;
    private readonly ILogger<SystemPromptService> _logger;
    private string? _cachedSystemPrompt;

    public SystemPromptService(ILogger<SystemPromptService> logger)
    {
        _logger = logger;
        _systemPromptsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Configuration", "SystemPrompts.md");
    }

    public async Task<string> GetSystemPromptAsync()
    {
        if (_cachedSystemPrompt != null)
            return _cachedSystemPrompt;

        try
        {
            if (!File.Exists(_systemPromptsPath))
            {
                _logger.LogWarning("System prompts file not found at: {Path}", _systemPromptsPath);
                return GetDefaultSystemPrompt();
            }

            _cachedSystemPrompt = await File.ReadAllTextAsync(_systemPromptsPath);
            _logger.LogInformation("System prompts loaded successfully from: {Path}", _systemPromptsPath);
            return _cachedSystemPrompt;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load system prompts from: {Path}", _systemPromptsPath);
            return GetDefaultSystemPrompt();
        }
    }

    public async Task<string> GetLocatorRulesAsync()
    {
        var fullPrompt = await GetSystemPromptAsync();
        return ExtractSection(fullPrompt, "## Locator Strategy Rules");
    }

    public async Task<string> GetVerificationRulesAsync()
    {
        var fullPrompt = await GetSystemPromptAsync();
        return ExtractSection(fullPrompt, "## Verification and Assertion Rules");
    }

    public async Task<string> GetErrorHandlingRulesAsync()
    {
        var fullPrompt = await GetSystemPromptAsync();
        return ExtractSection(fullPrompt, "## Error Handling Rules");
    }

    private string ExtractSection(string content, string sectionHeader)
    {
        var lines = content.Split('\n');
        var sectionStart = -1;
        var sectionEnd = lines.Length;

        for (int i = 0; i < lines.Length; i++)
        {
            if (lines[i].StartsWith(sectionHeader))
            {
                sectionStart = i;
            }
            else if (sectionStart >= 0 && lines[i].StartsWith("## ") && i > sectionStart)
            {
                sectionEnd = i;
                break;
            }
        }

        if (sectionStart >= 0)
        {
            return string.Join('\n', lines[sectionStart..sectionEnd]);
        }

        return string.Empty;
    }

    private string GetDefaultSystemPrompt()
    {
        return @"You are an expert test automation agent using Playwright MCP. 
Execute web automation tasks based on user instructions and return structured results.
Focus on performing actions with robust locator strategies and comprehensive error handling.";
    }
}
