namespace Bravo.AutomationAgent.Services.Logging;

public interface ITestLogger
{
    void Initialize(string environment, string testId);
    void LogInfo(string message, params object[] args);
    void LogWarning(string message, params object[] args);
    void LogError(string message, params object[] args);
    void LogError(Exception exception, string message, params object[] args);
    void LogDebug(string message, params object[] args);
    void LogStep(string stepName, string status, int duration, string? error = null);
    Task<string> GetLogContentAsync();
    Task FlushAsync();
    void Dispose();
}
