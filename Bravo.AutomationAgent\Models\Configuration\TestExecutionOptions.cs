namespace Bravo.AutomationAgent.Models.Configuration;

public class TestExecutionOptions
{
    public const string SectionName = "TestExecution";
    
    public int DefaultTimeout { get; set; } = 30000;
    public int MaxTimeout { get; set; } = 300000;
    public int RetryAttempts { get; set; } = 3;
    public bool EnableScreenshots { get; set; } = true;
    public bool EnableTracing { get; set; } = false;
    public bool SoftAssertions { get; set; } = true;
}

public class ScreenshotOptions
{
    public const string SectionName = "Screenshots";
    
    public string Path { get; set; } = "screenshots";
    public string Format { get; set; } = "png";
    public int Quality { get; set; } = 90;
    public bool FullPage { get; set; } = true;
}

public class TraceOptions
{
    public const string SectionName = "Traces";
    
    public string Path { get; set; } = "traces";
    public bool EnableVideo { get; set; } = false;
    public bool EnableScreenshots { get; set; } = true;
    public int RetentionDays { get; set; } = 7;
}
