using Bravo.AutomationAgent.Models.Configuration;
using Bravo.AutomationAgent.Models.Requests;
using Microsoft.Extensions.Options;
using System.Text.RegularExpressions;

namespace Bravo.AutomationAgent.Services.Validation;

public class TestCaseValidationService(
    IOptions<TestExecutionOptions> testExecutionOptions,
    ILogger<TestCaseValidationService> logger) : ITestCaseValidationService
{
    private readonly TestExecutionOptions _options = testExecutionOptions.Value;

    public ValidationResult ValidateTestCase(TestCaseRequest request)
    {
        var result = new ValidationResult();
        
        // Validate test case content
        var contentValidation = ValidateTestCaseContent(request.TestCase);
        if (!contentValidation.IsValid)
        {
            result.Errors.AddRange(contentValidation.Errors);
        }
        result.Warnings.AddRange(contentValidation.Warnings);
        
        // Validate timeout
        var timeoutValidation = ValidateTimeout(request.Timeout);
        if (!timeoutValidation.IsValid)
        {
            result.Errors.AddRange(timeoutValidation.Errors);
        }
        result.Warnings.AddRange(timeoutValidation.Warnings);
        
        // Validate parameters
        if (request.Parameters != null)
        {
            var paramValidation = ValidateParameters(request.Parameters);
            if (!paramValidation.IsValid)
            {
                result.Errors.AddRange(paramValidation.Errors);
            }
            result.Warnings.AddRange(paramValidation.Warnings);
        }
        
        // Validate user ID format
        if (!string.IsNullOrEmpty(request.UserId))
        {
            var userIdValidation = ValidateUserId(request.UserId);
            if (!userIdValidation.IsValid)
            {
                result.Errors.AddRange(userIdValidation.Errors);
            }
        }
        
        result.IsValid = !result.Errors.Any();
        
        logger.LogDebug("Test case validation completed. Valid: {IsValid}, Errors: {ErrorCount}, Warnings: {WarningCount}",
            result.IsValid, result.Errors.Count, result.Warnings.Count);
        
        return result;
    }

    public ValidationResult ValidateTimeout(int? timeout)
    {
        var result = new ValidationResult();
        
        if (!timeout.HasValue)
        {
            // Use default timeout
            return ValidationResult.Success();
        }
        
        if (timeout.Value <= 0)
        {
            result.AddError("Timeout must be greater than 0");
        }
        else if (timeout.Value > _options.MaxTimeout)
        {
            result.AddError($"Timeout cannot exceed {_options.MaxTimeout}ms");
        }
        else if (timeout.Value < 1000)
        {
            result.AddWarning("Timeout is very short (less than 1 second), this may cause test failures");
        }
        
        return result;
    }

    public ValidationResult ValidateTestCaseContent(string testCase)
    {
        var result = new ValidationResult();
        
        if (string.IsNullOrWhiteSpace(testCase))
        {
            result.AddError("Test case cannot be empty");
            return result;
        }
        
        if (testCase.Length > 10000)
        {
            result.AddError("Test case is too long (maximum 10,000 characters)");
        }
        
        // Check for potentially dangerous content
        var dangerousPatterns = new[]
        {
            @"<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>",
            @"javascript:",
            @"data:text\/html",
            @"eval\s*\(",
            @"document\.write"
        };
        
        foreach (var pattern in dangerousPatterns)
        {
            if (Regex.IsMatch(testCase, pattern, RegexOptions.IgnoreCase))
            {
                result.AddError($"Test case contains potentially dangerous content: {pattern}");
            }
        }
        
        // Check for common test case patterns
        var hasAction = ContainsActionKeywords(testCase);
        if (!hasAction)
        {
            result.AddWarning("Test case doesn't appear to contain any action keywords (click, navigate, fill, etc.)");
        }
        
        // Check for verification patterns
        var hasVerification = ContainsVerificationKeywords(testCase);
        if (!hasVerification)
        {
            result.AddWarning("Test case doesn't appear to contain any verification keywords (verify, assert, check, etc.)");
        }
        
        return result;
    }

    private ValidationResult ValidateParameters(Dictionary<string, string> parameters)
    {
        var result = new ValidationResult();
        
        if (parameters.Count > 50)
        {
            result.AddError("Too many parameters (maximum 50 allowed)");
        }
        
        foreach (var param in parameters)
        {
            if (string.IsNullOrWhiteSpace(param.Key))
            {
                result.AddError("Parameter key cannot be empty");
            }
            
            if (param.Key.Length > 100)
            {
                result.AddError($"Parameter key '{param.Key}' is too long (maximum 100 characters)");
            }
            
            if (param.Value?.Length > 1000)
            {
                result.AddError($"Parameter value for '{param.Key}' is too long (maximum 1000 characters)");
            }
        }
        
        return result;
    }

    private ValidationResult ValidateUserId(string userId)
    {
        var result = new ValidationResult();
        
        if (userId.Length > 50)
        {
            result.AddError("User ID is too long (maximum 50 characters)");
        }
        
        if (!Regex.IsMatch(userId, @"^[a-zA-Z0-9_-]+$"))
        {
            result.AddError("User ID can only contain letters, numbers, underscores, and hyphens");
        }
        
        return result;
    }

    private bool ContainsActionKeywords(string testCase)
    {
        var actionKeywords = new[]
        {
            "click", "navigate", "go to", "open", "fill", "type", "enter",
            "select", "choose", "upload", "download", "scroll", "drag", "drop"
        };
        
        return actionKeywords.Any(keyword => 
            testCase.Contains(keyword, StringComparison.OrdinalIgnoreCase));
    }

    private bool ContainsVerificationKeywords(string testCase)
    {
        var verificationKeywords = new[]
        {
            "verify", "assert", "check", "ensure", "confirm", "validate",
            "should", "expect", "must", "contains", "equals", "matches"
        };
        
        return verificationKeywords.Any(keyword => 
            testCase.Contains(keyword, StringComparison.OrdinalIgnoreCase));
    }
}
