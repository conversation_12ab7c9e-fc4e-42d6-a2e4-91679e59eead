# Sample Test Cases for Bravo Automation Framework

## Example 1: Simple Navigation and Verification

### Input (JSON):
```json
{
  "testCase": "Open tinhte forums\nOpen a random topic\nGet the first comment\nVerify the title is not blank",
  "timeout": 30000,
  "enableScreenshots": true,
  "enableTracing": false
}
```

### Expected Output:
```json
{
  "userPromt": "Open tinhte forums\nOpen a random topic\nGet the first comment\nVerify the title is not blank",
  "status": "Passed",
  "duration": 5000,
  "steps": [
    { "step": "Navigate to URL: https://tinhte.vn", "status": "Passed", "duration": 2000 },
    { "step": "Click element: random topic with title [Hôm nay ăn gì]", "status": "Passed", "duration": 1500 },
    { "step": "Get text from element: first comment", "status": "Passed", "duration": 1000 },
    { "step": "Verify text 'Bun bo' in element: first comment", "status": "Passed", "duration": 500 }
  ]
}
```

## Example 2: Form Filling and Submission

### Input (JSON):
```json
{
  "testCase": "Navigate to login page\nFill username field with 'testuser'\nFill password field with 'password123'\nClick login button\nVerify successful login",
  "parameters": {
    "baseUrl": "https://example.com",
    "username": "testuser",
    "password": "password123"
  },
  "timeout": 20000,
  "enableScreenshots": true
}
```

## Example 3: E-commerce Shopping Flow

### Input (JSON):
```json
{
  "testCase": "Open shopping website\nSearch for 'laptop'\nSelect first product\nAdd to cart\nGo to checkout\nVerify cart contains the product",
  "parameters": {
    "searchTerm": "laptop",
    "expectedProduct": "Gaming Laptop"
  },
  "timeout": 45000,
  "enableScreenshots": true,
  "enableTracing": true
}
```

## Example 4: Error Scenario

### Input (JSON):
```json
{
  "testCase": "Navigate to non-existent page\nVerify page loads successfully",
  "timeout": 10000,
  "enableScreenshots": true
}
```

### Expected Output (Error):
```json
{
  "userPromt": "Navigate to non-existent page\nVerify page loads successfully",
  "status": "Failed",
  "record": "traces/test_execution_20250626_143022",
  "duration": 8000,
  "steps": [
    { "step": "Navigate to URL: https://example.com/nonexistent", "status": "Passed", "duration": 3000 },
    { 
      "step": "Verify page loads successfully", 
      "status": "Failed", 
      "duration": 5000,
      "Error": "Page returned 404 Not Found",
      "Sreenshot": "screenshots/error_20250626_143025_verify_page_loads.png"
    }
  ]
}
```

## Example 5: Complex Multi-Step Workflow

### Input (JSON):
```json
{
  "testCase": "Open dashboard\nClick on reports section\nGenerate monthly report\nDownload report as PDF\nVerify download completed\nOpen downloaded file\nVerify report contains current month data",
  "parameters": {
    "reportType": "monthly",
    "format": "PDF",
    "month": "current"
  },
  "timeout": 60000,
  "enableScreenshots": true,
  "enableTracing": true,
  "userId": "test_user_001"
}
```

## Testing Guidelines

### 1. Basic Functionality Tests
- Simple navigation
- Element interaction (click, fill, select)
- Text verification
- Attribute verification

### 2. Error Handling Tests
- Invalid URLs
- Missing elements
- Timeout scenarios
- Network failures

### 3. Performance Tests
- Large page loads
- Multiple element interactions
- Long-running scenarios

### 4. Edge Cases
- Empty test cases
- Very long test cases
- Special characters in inputs
- Concurrent test execution

## API Endpoints for Testing

### Execute Test Case
```
POST /api/playwright/execute
Content-Type: application/json

{
  "testCase": "Your test case description here",
  "timeout": 30000,
  "enableScreenshots": true,
  "enableTracing": false
}
```

### Legacy Endpoint (Backward Compatibility)
```
POST /api/playwright/run
Content-Type: application/json

{
  "prompt": "Your test prompt here"
}
```

### Health Check
```
GET /api/playwright/health
```

## Response Format

All test executions return a structured JSON response with:
- `userPromt`: Original test case
- `status`: "Passed" or "Failed"
- `duration`: Total execution time in milliseconds
- `steps`: Array of individual test steps with timing and status
- `record`: Path to trace file (if enabled and test failed)

Each step includes:
- `step`: Description of the action performed
- `status`: "Passed" or "Failed"
- `duration`: Step execution time in milliseconds
- `Error`: Error message (if step failed)
- `Sreenshot`: Screenshot path (if step failed and screenshots enabled)
