using Bravo.AutomationAgent.Models.Responses;
using System.Text;

namespace Bravo.AutomationAgent.Services.ErrorHandling;

public class ErrorHandlingService(ILogger<ErrorHandlingService> logger, IConfiguration configuration) : IErrorHandlingService
{
    private readonly string _screenshotPath = configuration["Screenshots:Path"] ?? "screenshots";
    private readonly string _tracePath = configuration["Traces:Path"] ?? "traces";

    public async Task<string?> CaptureScreenshotAsync(string stepDescription, CancellationToken cancellationToken = default)
    {
        try
        {
            // Ensure screenshot directory exists
            Directory.CreateDirectory(_screenshotPath);
            
            var timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss_fff");
            var sanitizedDescription = SanitizeFileName(stepDescription);
            var fileName = $"error_{timestamp}_{sanitizedDescription}.png";
            var fullPath = Path.Combine(_screenshotPath, fileName);
            
            logger.LogInformation("Attempting to capture screenshot for step: {Step}", stepDescription);
            
            // Note: In a real implementation, this would use Playwright MCP to capture the screenshot
            // For now, we'll create a placeholder file to demonstrate the structure
            await CreatePlaceholderScreenshotAsync(fullPath, cancellationToken);
            
            logger.LogInformation("Screenshot captured: {Path}", fullPath);
            return fullPath;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to capture screenshot for step: {Step}", stepDescription);
            return null;
        }
    }

    public async Task<string?> CaptureTraceAsync(string testId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Ensure trace directory exists
            Directory.CreateDirectory(_tracePath);
            
            var timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
            var fileName = $"trace_{testId}_{timestamp}.zip";
            var fullPath = Path.Combine(_tracePath, fileName);
            
            logger.LogInformation("Attempting to capture trace for test: {TestId}", testId);
            
            // Note: In a real implementation, this would use Playwright MCP to capture the trace
            // For now, we'll create a placeholder file to demonstrate the structure
            await CreatePlaceholderTraceAsync(fullPath, cancellationToken);
            
            logger.LogInformation("Trace captured: {Path}", fullPath);
            return fullPath;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to capture trace for test: {TestId}", testId);
            return null;
        }
    }

    public async Task<TestStep> HandleStepErrorAsync(TestStep step, Exception exception, CancellationToken cancellationToken = default)
    {
        logger.LogError(exception, "Error in test step: {Step}", step.Step);
        
        // Update step with error information
        step.Status = "Failed";
        step.Error = GenerateErrorReport(step, exception);
        
        // Capture screenshot if the step failed
        var screenshotPath = await CaptureScreenshotAsync(step.Step, cancellationToken);
        if (!string.IsNullOrEmpty(screenshotPath))
        {
            step.Screenshot = screenshotPath;
        }
        
        return step;
    }

    public async Task<MissionResult> HandleTestErrorAsync(MissionResult result, Exception exception, CancellationToken cancellationToken = default)
    {
        logger.LogError(exception, "Error in test execution: {UserPrompt}", result.UserPrompt);
        
        // Update overall result status
        result.Status = "Failed";
        
        // Capture trace for the entire test
        var tracePath = await CaptureTraceAsync(result.Id, cancellationToken);
        if (!string.IsNullOrEmpty(tracePath))
        {
            result.Record = tracePath;
        }
        
        // Add error step if no steps exist
        if (!result.Steps.Any())
        {
            result.Steps.Add(new TestStep
            {
                Step = "Test execution failed",
                Status = "Failed",
                Duration = 0,
                Error = GenerateErrorReport(null, exception)
            });
        }
        else
        {
            // Update the last step with error information if it doesn't already have an error
            var lastStep = result.Steps.Last();
            if (string.IsNullOrEmpty(lastStep.Error))
            {
                lastStep.Status = "Failed";
                lastStep.Error = GenerateErrorReport(lastStep, exception);
                
                // Capture screenshot for the failed step
                var screenshotPath = await CaptureScreenshotAsync(lastStep.Step, cancellationToken);
                if (!string.IsNullOrEmpty(screenshotPath))
                {
                    lastStep.Screenshot = screenshotPath;
                }
            }
        }
        
        return result;
    }

    public string GenerateErrorReport(TestStep? step, Exception exception)
    {
        var report = new StringBuilder();
        
        if (step != null)
        {
            report.AppendLine($"Step: {step.Step}");
            report.AppendLine($"Status: {step.Status}");
            report.AppendLine($"Duration: {step.Duration}ms");
            report.AppendLine();
        }
        
        report.AppendLine($"Error Type: {exception.GetType().Name}");
        report.AppendLine($"Error Message: {exception.Message}");
        
        if (exception.InnerException != null)
        {
            report.AppendLine($"Inner Exception: {exception.InnerException.Message}");
        }
        
        // Add stack trace for debugging (truncated for readability)
        var stackTrace = exception.StackTrace;
        if (!string.IsNullOrEmpty(stackTrace))
        {
            var lines = stackTrace.Split('\n').Take(5); // First 5 lines only
            report.AppendLine("Stack Trace (truncated):");
            foreach (var line in lines)
            {
                report.AppendLine($"  {line.Trim()}");
            }
        }
        
        return report.ToString();
    }

    private string SanitizeFileName(string fileName)
    {
        var invalidChars = Path.GetInvalidFileNameChars();
        var sanitized = new string(fileName.Where(c => !invalidChars.Contains(c)).ToArray());
        
        // Limit length and remove extra spaces
        sanitized = sanitized.Replace(" ", "_").Trim();
        if (sanitized.Length > 50)
        {
            sanitized = sanitized.Substring(0, 50);
        }
        
        return sanitized;
    }

    private async Task CreatePlaceholderScreenshotAsync(string path, CancellationToken cancellationToken)
    {
        // Create a minimal PNG file as placeholder
        // In real implementation, this would be handled by Playwright MCP
        var placeholderContent = Convert.FromBase64String(
            "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==");
        await File.WriteAllBytesAsync(path, placeholderContent, cancellationToken);
    }

    private async Task CreatePlaceholderTraceAsync(string path, CancellationToken cancellationToken)
    {
        // Create a minimal ZIP file as placeholder
        // In real implementation, this would be handled by Playwright MCP
        var placeholderContent = Encoding.UTF8.GetBytes("Placeholder trace file");
        await File.WriteAllBytesAsync(path, placeholderContent, cancellationToken);
    }
}
