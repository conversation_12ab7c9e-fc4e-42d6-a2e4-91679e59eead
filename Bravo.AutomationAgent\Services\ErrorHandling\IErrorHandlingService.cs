using Bravo.AutomationAgent.Models.Responses;

namespace Bravo.AutomationAgent.Services.ErrorHandling;

public interface IErrorHandlingService
{
    Task<string?> CaptureScreenshotAsync(string stepDescription, CancellationToken cancellationToken = default);
    Task<string?> CaptureTraceAsync(string testId, CancellationToken cancellationToken = default);
    Task<TestStep> HandleStepErrorAsync(TestStep step, Exception exception, CancellationToken cancellationToken = default);
    Task<MissionResult> HandleTestErrorAsync(MissionResult result, Exception exception, CancellationToken cancellationToken = default);
    string GenerateErrorReport(TestStep step, Exception exception);
}
